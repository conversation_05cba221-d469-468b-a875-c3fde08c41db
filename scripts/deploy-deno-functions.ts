// Load environment variables
require('dotenv').config({ path: ".env.local" });

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { fileState } from '../src/lib/db/schema';
import { eq, desc } from 'drizzle-orm';
import { DenoArtifactHandler } from '../src/lib/integrations/deno/DenoArtifactHandler';
import { FileItem } from '../src/types/file';

// Get database URL from environment variables
const DATABASE_URL = process.env.POSTGRES_URL;
if (!DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
}

// Create a new database connection
const client = postgres(DATABASE_URL);
const db = drizzle(client);

/**
 * Script to deploy all Deno functions for a specific project
 */
async function deployDenoFunctions() {
    const projectId = '36c6264f-4622-45d5-899c-823b81058456';
    
    console.log(`🚀 Starting Deno function deployment for project: ${projectId}`);
    
    try {
        // Get the latest file state for the project
        console.log('📁 Fetching latest file state from database...');

        const latestFileStateResult = await db
            .select()
            .from(fileState)
            .where(eq(fileState.projectId, projectId))
            .orderBy(desc(fileState.createdAt))
            .limit(1);

        if (latestFileStateResult.length === 0) {
            console.log('❌ No file state found for project');
            return;
        }

        const files = latestFileStateResult[0].files as FileItem[];
        console.log(`📄 Found ${files.length} total files in project`);

        // Filter for Deno function files
        const denoFunctionFiles = files.filter(file => {
            const isDenoFunction = file.name.includes('magically/functions/') && 
                                 (file.name.endsWith('.ts') || file.name.endsWith('.js'));
            
            if (isDenoFunction) {
                console.log(`✅ Found Deno function file: ${file.name}`);
            }
            
            return isDenoFunction;
        });

        if (denoFunctionFiles.length === 0) {
            console.log('❌ No Deno function files found in magically/functions/ folder');
            console.log('📋 Available files:');
            files.forEach(file => {
                console.log(`   - ${file.name}`);
            });
            return;
        }

        console.log(`🎯 Found ${denoFunctionFiles.length} Deno function files to deploy:`);
        denoFunctionFiles.forEach(file => {
            console.log(`   - ${file.name} (${file.content.length} chars)`);
        });

        // Group functions by their directory (function name)
        const functionsByName = new Map<string, FileItem[]>();

        for (const file of denoFunctionFiles) {
            // Expected path format: magically/functions/function-name/index.ts
            const pathParts = file.name.split('/');
            const functionDirIndex = pathParts.findIndex((part: string) => part === 'functions') + 1;
            
            if (functionDirIndex <= 0 || functionDirIndex >= pathParts.length) {
                console.warn(`⚠️  Invalid function path: ${file.name}. Expected format: magically/functions/function-name/index.ts`);
                continue;
            }
            
            const functionName = pathParts[functionDirIndex];
            console.log(`📦 Processing function: ${functionName} from file: ${file.name}`);
            
            // Get all files for this function
            const functionAllFiles = files.filter(f => {
                const parts = f.name.split('/');
                const idx = parts.findIndex((part: string) => part === 'functions') + 1;
                return idx > 0 && idx < parts.length && parts[idx] === functionName;
            });
            
            if (!functionsByName.has(functionName)) {
                functionsByName.set(functionName, functionAllFiles);
                console.log(`📁 Function ${functionName} has ${functionAllFiles.length} files`);
            }
        }

        // Initialize Deno artifact handler
        console.log('🔧 Initializing Deno artifact handler...');
        const denoHandler = new DenoArtifactHandler();

        // Deploy each function
        for (const [functionName, functionFiles] of functionsByName.entries()) {
            console.log(`\n🚀 Deploying function: ${functionName}`);
            console.log(`📁 Files to deploy:`);
            functionFiles.forEach(file => {
                console.log(`   - ${file.name}`);
            });

            try {
                // Create a mock data stream for logging
                const mockDataStream = {
                    writeData: (data: any) => {
                        console.log(`📡 Stream data:`, JSON.stringify(data, null, 2));
                    }
                };

                // Get all dirty file paths (simulate all files being dirty for deployment)
                const dirtyFilePaths = functionFiles.map(file => file.name);

                // Deploy the function
                await denoHandler.processFunctions(
                    files,
                    projectId,
                    mockDataStream,
                    dirtyFilePaths
                );

                console.log(`✅ Successfully processed function: ${functionName}`);
            } catch (error) {
                console.error(`❌ Error deploying function ${functionName}:`, error);
            }
        }

        console.log('\n🎉 Deployment process completed!');
        
    } catch (error) {
        console.error('❌ Error in deployment script:', error);
    } finally {
        // Close database connection
        await client.end();
        process.exit(0);
    }
}

// Run the script
deployDenoFunctions().catch(console.error);
