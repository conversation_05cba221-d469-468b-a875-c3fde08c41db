// Load environment variables
require('dotenv').config({ path: ".env.local" });

/**
 * Test MongoDB connection to debug the SSL issue
 */
async function testMongoConnection() {
    console.log('🧪 Testing MongoDB connection...');
    
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
        console.error('❌ MONGODB_URI not found in environment variables');
        return;
    }
    
    console.log('🔗 MongoDB URI found:', mongoUri.substring(0, 20) + '...');
    
    try {
        // Test with Node.js MongoDB driver
        const { MongoClient } = require('mongodb');
        
        console.log('📡 Attempting connection with Node.js driver...');
        const client = new MongoClient(mongoUri);
        await client.connect();
        console.log('✅ Node.js MongoDB connection successful!');
        
        // Test database access
        const db = client.db('app_a5992640182a460dacdcedfde0243282');
        const collections = await db.listCollections().toArray();
        console.log('📁 Available collections:', collections.map(c => c.name));
        
        await client.close();
        console.log('🔒 Connection closed');
        
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error.message);
        console.error('Stack:', error.stack);
    }
}

testMongoConnection().catch(console.error);
