#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to extract messages from a date range and save them to a JSON file
 * Usage: npx tsx scripts/extract-messages.ts --startDate 2025-06-10 --endDate 2025-06-11
 * Or for a single date: npx tsx scripts/extract-messages.ts --date 2025-06-10
 */
import dotenv from 'dotenv';
import { program } from 'commander';
import fs from 'fs';
import path from 'path';
import { encode } from 'gpt-tokenizer';

// Load environment variables
require('dotenv').config({ path: ".env.local" });

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { message } from '../src/lib/db/schema';
import { eq, and, gte, lt } from 'drizzle-orm';

// Get database URL from environment variables
const DATABASE_URL = process.env.POSTGRES_URL;
if (!DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
}

// Create a new database connection
const client = postgres(DATABASE_URL);
const db = drizzle(client);

// Define command line options
program
    .option('-d, --date <date>', 'Single date to extract messages from (YYYY-MM-DD)')
    .option('-s, --startDate <date>', 'Start date for extraction (YYYY-MM-DD)')
    .option('-e, --endDate <date>', 'End date for extraction (YYYY-MM-DD)')
    .option('-o, --output <path>', 'Output file path', './message-extract.json')
    .parse(process.argv);

const options = program.opts();

// Validate date inputs
if (!options.date && !(options.startDate && options.endDate)) {
    console.error('Please provide either a single date or both start and end dates');
    program.help();
    process.exit(1);
}

// Parse dates
let startDate: Date, endDate: Date;

if (options.date) {
    startDate = new Date(`${options.date}T00:00:00`);
    endDate = new Date(`${options.date}T23:59:59.999`);
} else {
    startDate = new Date(`${options.startDate}T00:00:00`);
    endDate = new Date(`${options.endDate}T23:59:59.999`);
}

// Validate date parsing
if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    console.error('Invalid date format. Please use YYYY-MM-DD');
    process.exit(1);
}

// Ensure output directory exists
const outputDir = path.dirname(options.output);
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

async function extractMessages() {
    try {
        console.log(`Starting message extraction from ${startDate.toISOString()} to ${endDate.toISOString()}...`);
        
        // Query messages within the date range
        const messages = await db
            .select()
            .from(message)
            .where(
                and(
                    gte(message.createdAt, startDate),
                    lt(message.createdAt, endDate)
                )
            )
            .orderBy(message.createdAt);

        console.log(`Found ${messages.length} messages in the specified date range.`);

        // Process each message to extract toolName and other content data
        const processedMessages = messages.map(msg => {
            const processedMsg: any = { ...msg };
            
            // Initialize token counts and costs
            processedMsg.inputTokens = 0;
            processedMsg.outputTokens = 0;
            processedMsg.inputCost = 0;
            processedMsg.outputCost = 0;
            
            // Calculate tokens based on role
            if (msg.role === 'assistant' && msg.content) {
                // For assistant messages, content contributes to outputTokens
                try {
                    const contentStr = typeof msg.content === 'string' 
                        ? msg.content 
                        : JSON.stringify(msg.content);
                    processedMsg.outputTokens = encode(contentStr).length;
                    // Calculate cost: $15 per million output tokens
                    processedMsg.outputCost = (processedMsg.outputTokens / 1000000) * 15;
                } catch (error) {
                    console.warn(`Error calculating tokens for assistant message ${msg.id}:`, error);
                }
            } else if (msg.role === 'tool' && msg.content) {
                try {
                    // For tool messages, entire content contributes to inputTokens
                    const contentStr = typeof msg.content === 'string' 
                        ? msg.content 
                        : JSON.stringify(msg.content);
                    processedMsg.inputTokens += encode(contentStr).length;
                    
                    // Extract toolName and other data from content
                    const content = Array.isArray(msg.content) ? msg.content : [msg.content];
                    
                    // Extract toolName from content
                    const toolData = content.find(item => item.toolName || (item.type === 'tool-result' && item.toolName));
                    if (toolData) {
                        processedMsg.toolName = toolData.toolName;
                        
                        // Extract result data if available and calculate additional inputTokens
                        if (toolData.result) {
                            processedMsg.toolResult = toolData.result;
                            
                            // Calculate tokens for the result
                            const resultStr = typeof toolData.result === 'string' 
                                ? toolData.result 
                                : JSON.stringify(toolData.result);
                            processedMsg.inputTokens += encode(resultStr).length;
                        }
                    }
                    
                    // Aggregate all results from content array items
                    let aggregatedResults = '';
                    content.forEach(item => {
                        if (item.result) {
                            const resultStr = typeof item.result === 'string' 
                                ? item.result 
                                : JSON.stringify(item.result);
                            aggregatedResults += resultStr;
                        }
                    });
                    
                    // Calculate tokens for aggregated results (if not already counted above)
                    if (aggregatedResults && !toolData?.result) {
                        processedMsg.inputTokens += encode(aggregatedResults).length;
                    }
                    
                    // Calculate cost: $3 per million input tokens
                    processedMsg.inputCost = (processedMsg.inputTokens / 1000000) * 3;
                    
                } catch (error) {
                    console.warn(`Error processing content for tool message ${msg.id}:`, error);
                }
            }
            
            return processedMsg;
        });

        // Write to output file
        fs.writeFileSync(
            options.output,
            JSON.stringify(processedMessages, null, 2)
        );

        console.log(`Successfully extracted ${processedMessages.length} messages to ${options.output}`);
        
    } catch (error) {
        console.error('Error extracting messages:', error);
        throw error;
    }
}

// Check if gpt-tokenizer is installed, if not provide instructions
try {
    require.resolve('gpt-tokenizer');
} catch (e) {
    console.error('\nError: gpt-tokenizer package is required for token counting.');
    console.error('Please install it using: npm install gpt-tokenizer\n');
    process.exit(1);
}

// Run the extraction function
extractMessages()
    .then(() => {
        console.log('Script completed successfully');
        return client.end();
    })
    .then(() => {
        console.log('Database connection closed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error during extraction:', error);
        client.end().finally(() => {
            process.exit(1);
        });
    });
