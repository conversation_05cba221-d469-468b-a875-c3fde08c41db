// Load environment variables
require('dotenv').config({ path: ".env.local" });

import { MongoDBAtlasAdminClient } from '../src/lib/integrations/mongodb-atlas/MongoDBAtlasAdminClient';
import { encrypt, decrypt, testEncryption } from '../src/lib/utils/encryption';

/**
 * Test MongoDB Atlas Admin API integration
 */
async function testMongoDBAtlasIntegration() {
    console.log('🧪 Testing MongoDB Atlas Admin API integration...');
    
    try {
        // Test encryption first
        console.log('\n🔐 Testing encryption...');
        const encryptionWorks = testEncryption();
        if (!encryptionWorks) {
            console.error('❌ Encryption test failed');
            return;
        }
        console.log('✅ Encryption test passed');
        
        // Test Atlas API connection
        console.log('\n🔗 Testing Atlas API connection...');
        const atlasClient = new MongoDBAtlasAdminClient();
        
        const connectionWorks = await atlasClient.testConnection();
        if (!connectionWorks) {
            console.error('❌ Atlas API connection failed');
            return;
        }
        console.log('✅ Atlas API connection successful');
        
        // List existing users
        console.log('\n👥 Listing existing database users...');
        const existingUsers = await atlasClient.listDatabaseUsers();
        console.log(`Found ${existingUsers.length} existing users:`);
        existingUsers.forEach(user => {
            console.log(`  - ${user.username} (roles: ${user.roles.map(r => `${r.roleName}@${r.databaseName}`).join(', ')})`);
        });
        
        // Test creating a user for a test project
        const testProjectId = 'test-project-12345';
        console.log(`\n🔧 Creating test user for project: ${testProjectId}`);
        
        try {
            const mongoUser = await atlasClient.createDatabaseUser(testProjectId);
            console.log('✅ User created successfully:', {
                username: mongoUser.username,
                database: mongoUser.roles[0].db,
                role: mongoUser.roles[0].role
            });
            
            // Test connection string generation
            const connectionString = atlasClient.generateConnectionString(mongoUser);
            console.log('🔗 Generated connection string:', connectionString.substring(0, 50) + '...');
            
            // Test encryption of password
            const encryptedPassword = encrypt(mongoUser.password);
            console.log('🔐 Encrypted password length:', encryptedPassword.length);
            
            const decryptedPassword = decrypt(encryptedPassword);
            const passwordsMatch = decryptedPassword === mongoUser.password;
            console.log('🔓 Password encryption/decryption:', passwordsMatch ? '✅ Success' : '❌ Failed');
            
            // Clean up - delete the test user
            console.log(`\n🗑️ Cleaning up test user...`);
            await atlasClient.deleteDatabaseUser(testProjectId);
            console.log('✅ Test user deleted successfully');
            
        } catch (userError) {
            console.error('❌ User creation/deletion failed:', userError.message);
        }
        
        console.log('\n🎉 MongoDB Atlas integration test completed successfully!');
        
    } catch (error) {
        console.error('❌ MongoDB Atlas integration test failed:', error);
    }
}

testMongoDBAtlasIntegration().catch(console.error);
