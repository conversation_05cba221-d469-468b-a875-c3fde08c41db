# Deno Subhosting Auto-Deployment Example

## How it works

When the LLM creates files in the `magically/functions/` folder, they are automatically deployed to Deno Subhosting with MongoDB URI and other environment variables injected.

## Example: Todo API Function

The LLM would create this file structure:

```
magically/functions/todo-api/index.ts
```

## Example Function Content

```typescript
// magically/functions/todo-api/index.ts

import { MongoClient } from "https://deno.land/x/mongo@v0.32.0/mod.ts";

// Environment variables are automatically injected by DenoArtifactHandler
const MONGODB_URI = Deno.env.get("MONGODB_URI")!;
const APP_ID = Deno.env.get("APP_ID")!;

const client = new MongoClient();
await client.connect(MONGODB_URI);
const db = client.database(`app_${APP_ID}`);

export default async function handler(req: Request): Promise<Response> {
  const url = new URL(req.url);
  const method = req.method;

  // CORS headers
  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
    "Content-Type": "application/json",
  };

  if (method === "OPTIONS") {
    return new Response(null, { status: 200, headers });
  }

  try {
    if (method === "GET" && url.pathname === "/todos") {
      // Get all todos
      const todos = await db.collection("todos").find({}).toArray();
      return new Response(JSON.stringify(todos), { headers });
    }

    if (method === "POST" && url.pathname === "/todos") {
      // Create new todo
      const body = await req.json();
      const todo = {
        ...body,
        id: crypto.randomUUID(),
        completed: false,
        createdAt: new Date(),
      };
      
      await db.collection("todos").insertOne(todo);
      return new Response(JSON.stringify(todo), { headers });
    }

    if (method === "PUT" && url.pathname.startsWith("/todos/")) {
      // Update todo
      const todoId = url.pathname.split("/")[2];
      const body = await req.json();
      
      await db.collection("todos").updateOne(
        { id: todoId },
        { $set: { ...body, updatedAt: new Date() } }
      );
      
      const updatedTodo = await db.collection("todos").findOne({ id: todoId });
      return new Response(JSON.stringify(updatedTodo), { headers });
    }

    if (method === "DELETE" && url.pathname.startsWith("/todos/")) {
      // Delete todo
      const todoId = url.pathname.split("/")[2];
      await db.collection("todos").deleteOne({ id: todoId });
      return new Response(JSON.stringify({ success: true }), { headers });
    }

    return new Response(JSON.stringify({ error: "Not found" }), { 
      status: 404, 
      headers 
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), { 
      status: 500, 
      headers 
    });
  }
}
```

## What Happens Automatically

1. **LLM creates the file** in `magically/functions/todo-api/index.ts`
2. **DenoArtifactHandler detects** the file change
3. **Auto-deploys** to Deno Subhosting
4. **Injects environment variables**:
   - `MONGODB_URI` - Connection to your MongoDB cluster
   - `APP_ID` - The specific app ID (e.g., "123")
   - `FUNCTION_NAME` - The function name (e.g., "todo-api")
5. **Returns deployment URL** to the user

## Generated App Usage

The React Native app would then use the deployed API:

```typescript
// In the generated React Native app
const API_BASE = "https://magically-123-todo-api.deno.dev";

const todos = await fetch(`${API_BASE}/todos`).then(r => r.json());
await fetch(`${API_BASE}/todos`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ title: 'New todo' })
});
```

## Benefits

- **Zero setup** - LLM just creates files, deployment happens automatically
- **Secure secrets** - MongoDB URI injected via environment variables
- **Isolated databases** - Each app gets `app_${APP_ID}` database
- **Instant deployment** - 2-5 second deployments via Deno Subhosting
- **Global edge** - Functions deployed to edge locations worldwide
- **LLM-friendly** - Standard REST API patterns LLMs know well
