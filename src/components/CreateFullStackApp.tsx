'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, Download } from 'lucide-react';

interface CreateFullStackAppProps {
  onAppCreated?: (appData: any) => void;
}

export default function CreateFullStackApp({ onAppCreated }: CreateFullStackAppProps) {
  const [appName, setAppName] = useState('');
  const [appIdea, setAppIdea] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleCreateApp = async () => {
    if (!appName.trim() || !appIdea.trim()) {
      setError('Please provide both app name and description');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/create-fullstack-app', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appName: appName.trim(),
          appIdea: appIdea.trim(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create app');
      }

      setResult(data);
      onAppCreated?.(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const downloadAppCode = () => {
    if (!result?.appCode) return;

    // Create a zip-like structure by creating multiple files
    Object.entries(result.appCode).forEach(([filename, content]) => {
      const blob = new Blob([content as string], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    });
  };

  if (result) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-500" />
            App Created Successfully!
          </CardTitle>
          <CardDescription>
            Your full-stack app "{result.project.name}" is ready to use
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert>
            <AlertDescription>
              {result.message}
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">Project Details</h3>
              <ul className="text-sm space-y-1">
                <li><strong>Name:</strong> {result.project.name}</li>
                <li><strong>Slug:</strong> {result.project.slug}</li>
                <li><strong>Convex URL:</strong> {result.convexProject.url}</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">Authentication</h3>
              <ul className="text-sm space-y-1">
                <li><strong>Client ID:</strong> {result.oauthConfig.clientId.slice(0, 8)}...</li>
                <li><strong>Auth URL:</strong> {result.oauthConfig.authUrl}</li>
                <li><strong>Token URL:</strong> {result.oauthConfig.tokenUrl}</li>
              </ul>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Database Schema</h3>
            <div className="bg-gray-100 p-3 rounded text-sm">
              <pre>{JSON.stringify(result.schema, null, 2)}</pre>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Next Steps</h3>
            <ol className="list-decimal list-inside text-sm space-y-1">
              {result.instructions.map((instruction: string, index: number) => (
                <li key={index}>{instruction}</li>
              ))}
            </ol>
          </div>

          <div className="flex gap-4">
            <Button onClick={downloadAppCode} className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Download App Code
            </Button>
            <Button 
              variant="outline" 
              onClick={() => {
                setResult(null);
                setAppName('');
                setAppIdea('');
              }}
            >
              Create Another App
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Create Full-Stack App</CardTitle>
        <CardDescription>
          Describe your app idea and we'll create a complete React Native app with authentication, database, and backend in minutes.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <label htmlFor="appName" className="text-sm font-medium">
            App Name
          </label>
          <Input
            id="appName"
            placeholder="e.g., TaskMaster, ChatApp, ShopEasy"
            value={appName}
            onChange={(e) => setAppName(e.target.value)}
            disabled={loading}
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="appIdea" className="text-sm font-medium">
            App Description
          </label>
          <Textarea
            id="appIdea"
            placeholder="Describe your app idea... e.g., 'A todo app where users can create tasks, mark them as complete, and organize them by priority'"
            value={appIdea}
            onChange={(e) => setAppIdea(e.target.value)}
            disabled={loading}
            rows={4}
          />
        </div>

        <Button 
          onClick={handleCreateApp} 
          disabled={loading || !appName.trim() || !appIdea.trim()}
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Your App...
            </>
          ) : (
            'Create Full-Stack App'
          )}
        </Button>

        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>What you'll get:</strong></p>
          <ul className="list-disc list-inside space-y-1">
            <li>Complete React Native app with Expo</li>
            <li>Google authentication (no setup required)</li>
            <li>Real-time database with Convex</li>
            <li>Generated CRUD operations</li>
            <li>Ready to run with "expo start"</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
