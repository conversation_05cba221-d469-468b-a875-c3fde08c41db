/**
 * LLM API Service - Core logic and instructions for LLM API endpoints
 */

import { generateText, generateObject, streamText, experimental_generateImage as generateImage } from 'ai';
import { customModel } from '@/lib/ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import { put } from '@vercel/blob';

// Whitelisted models
export const WHITELISTED_MODELS = {
  text: [
    'openai/gpt-4.1-mini',
    'openai/gpt-4.1-nano',
    'openai/gpt-4.1',
    'openai/gpt-4o-mini',
    'openai/gpt-o3-mini-high',
    'google/gemini-2.5-flash',
    'anthropic/claude-3.5-haiku'
  ],
  image: [
    'dall-e-2',
    'dall-e-3'
  ]
} as const;

// Helper function to convert JSON schema to Zod schema
function jsonSchemaToZod(jsonSchema: any): z.ZodType<any> {
  if (!jsonSchema || !jsonSchema.properties) {
    return z.object({});
  }

  const zodProperties: Record<string, z.ZodType<any>> = {};

  for (const [key, value] of Object.entries(jsonSchema.properties)) {
    const prop = value as any;

    switch (prop.type) {
      case 'string':
        zodProperties[key] = z.string();
        break;
      case 'number':
        zodProperties[key] = z.number();
        break;
      case 'boolean':
        zodProperties[key] = z.boolean();
        break;
      case 'array':
        if (prop.items?.type === 'string') {
          zodProperties[key] = z.array(z.string());
        } else if (prop.items?.type === 'number') {
          zodProperties[key] = z.array(z.number());
        } else if (prop.items?.type === 'object') {
          zodProperties[key] = z.array(jsonSchemaToZod(prop.items));
        } else {
          zodProperties[key] = z.array(z.any());
        }
        break;
      case 'object':
        zodProperties[key] = jsonSchemaToZod(prop);
        break;
      default:
        zodProperties[key] = z.any();
    }
  }

  return z.object(zodProperties);
}

export class LLMAPIService {
  /**
   * Validate if a model is whitelisted
   */
  static validateModel(model: string, type: 'text' | 'image'): boolean {
    return WHITELISTED_MODELS[type].includes(model as any);
  }

  /**
   * Get list of available models
   */
  static getAvailableModels() {
    return {
      text: WHITELISTED_MODELS.text,
      image: WHITELISTED_MODELS.image,
      default: {
        text: 'openai/gpt-4.1-nano',
        image: 'dall-e-3'
      }
    };
  }

  /**
   * Handle invoke endpoint logic
   */
  static async handleInvoke(body: any) {
    const { prompt, response_json_schema, model = 'openai/gpt-4.1-nano', temperature = 0.7 } = body;

    if (!prompt) {
      throw { status: 400, error: 'Prompt is required' };
    }

    if (!this.validateModel(model, 'text')) {
      throw {
        status: 400,
        error: `Model ${model} is not allowed. Please contact Magically support to get it whitelisted.`
      };
    }

    // Validate and clamp temperature
    const clampedTemperature = Math.max(0, Math.min(1, temperature));

    try {
      if (response_json_schema) {
        const zodSchema = jsonSchemaToZod(response_json_schema);
        const result = await generateObject({
          model: customModel(model),
          prompt,
          schema: zodSchema,
          temperature: clampedTemperature,
        });
        return result.object;
      } else {
        const result = await generateText({
          model: customModel(model),
          prompt,
          temperature: clampedTemperature,
        });
        return { text: result.text };
      }
    } catch (error) {
      console.error('Error in LLM invoke:', error);
      throw { status: 500, error: 'Failed to generate response' };
    }
  }

  /**
   * Handle chat endpoint logic
   */
  static async handleChat(body: any) {
    const { messages, model = 'openai/gpt-4.1-nano', temperature = 0.7, stream = false } = body;

    if (!messages || !Array.isArray(messages)) {
      throw { status: 400, error: 'Messages array is required' };
    }

    if (messages.length === 0) {
      throw { status: 400, error: 'Messages array cannot be empty' };
    }

    if (!this.validateModel(model, 'text')) {
      throw {
        status: 400,
        error: `Model ${model} is not allowed. Please contact Magically support to get it whitelisted.`
      };
    }

    // Validate and clamp temperature
    const clampedTemperature = Math.max(0, Math.min(1, temperature));

    // Validate message format
    for (const message of messages) {
      if (!message.role || !message.content) {
        throw { status: 400, error: 'Each message must have role and content fields' };
      }

      const validRoles = ['system', 'user', 'assistant'];
      if (!validRoles.includes(message.role)) {
        throw {
          status: 400,
          error: `Invalid message role: ${message.role}. Must be one of: ${validRoles.join(', ')}`
        };
      }
    }

    try {
      if (stream) {
        const result = streamText({
          model: customModel(model),
          messages,
          temperature: clampedTemperature,
        });
        return { stream: result };
      } else {
        const result = await generateText({
          model: customModel(model),
          messages,
          temperature: clampedTemperature,
        });
        return {
          message: {
            role: 'assistant',
            content: result.text
          },
          usage: result.usage
        };
      }
    } catch (error) {
      console.error('Error in LLM chat:', error);
      throw { status: 500, error: 'Failed to generate chat response' };
    }
  }

  /**
   * Handle image endpoint logic
   */
  static async handleImage(body: any) {
    const { prompt, model = 'dall-e-3', size = '1024x1024', quality = 'standard', n = 1 } = body;

    if (!prompt) {
      throw { status: 400, error: 'Prompt is required' };
    }

    if (!this.validateModel(model, 'image')) {
      throw {
        status: 400,
        error: `Model ${model} is not allowed. Please contact Magically support to get it whitelisted.`
      };
    }

    const validSizes = ['1024x1024', '1792x1024', '1024x1792'];
    if (!validSizes.includes(size)) {
      throw {
        status: 400,
        error: `Invalid size: ${size}. Must be one of: ${validSizes.join(', ')}`
      };
    }

    if (n < 1 || n > 10) {
      throw { status: 400, error: 'Number of images (n) must be between 1 and 10' };
    }

    if (model === 'dall-e-3' && n > 1) {
      throw { status: 400, error: 'DALL-E 3 only supports generating 1 image at a time (n=1)' };
    }

    try {
      const providerOptions: any = { openai: {} };
      if (model === 'dall-e-3') {
        providerOptions.openai.quality = quality as 'standard' | 'hd';
      }

      const result = await generateImage({
        model: openai.image(model),
        prompt,
        size: size as '1024x1024' | '1792x1024' | '1024x1792',
        n,
        providerOptions,
      });

      // Save to Vercel Blob and return URLs
      if (n === 1) {
        const imageBuffer = Buffer.from(result.image.base64, 'base64');
        const timestamp = Date.now();
        const filename = `llm-images/${timestamp}-${model.replace('dall-e-', 'dalle')}.png`;

        const blob = await put(filename, imageBuffer, {
          access: 'public',
          contentType: 'image/png',
        });

        return {
          url: blob.url,
          filename: blob.pathname,
          prompt,
          model,
          size,
          quality,
          downloadUrl: blob.downloadUrl
        };
      } else {
        const uploadPromises = result.images.map(async (img, index) => {
          const imageBuffer = Buffer.from(img.base64, 'base64');
          const timestamp = Date.now();
          const filename = `llm-images/${timestamp}-${model.replace('dall-e-', 'dalle')}-${index + 1}.png`;

          const blob = await put(filename, imageBuffer, {
            access: 'public',
            contentType: 'image/png',
          });

          return {
            url: blob.url,
            filename: blob.pathname,
            downloadUrl: blob.downloadUrl
          };
        });

        const uploadedImages = await Promise.all(uploadPromises);
        return {
          images: uploadedImages,
          prompt,
          model,
          size,
          count: result.images.length
        };
      }
    } catch (error) {
      console.error('Error in image generation:', error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
        throw { status: 429, error: 'Rate limit exceeded. Please try again later.' };
      } else if (errorMessage.includes('content policy') || errorMessage.includes('safety')) {
        throw { status: 400, error: 'Image prompt violates content policy. Please modify your prompt.' };
      } else {
        throw { status: 500, error: `Failed to generate image: ${errorMessage}` };
      }
    }
  }

  /**
   * Get concise instructions for LLM to understand how to use the LLM API endpoints
   */
  static getLLMInstructions(): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://trymagically.com';
    
    return `
## LLM API - Built-in AI endpoints

**Base:** ${baseUrl}/api/llm/

**Endpoints:**
- POST /invoke - Text/structured output
- POST /chat - Conversations
- POST /image - Image generation
- GET /models - Available models

**Usage:**
\`\`\`typescript
// Text generation
fetch('${baseUrl}/api/llm/invoke', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ prompt: 'Write a tagline for a fitness app' })
});

// Structured data
fetch('${baseUrl}/api/llm/invoke', {
  method: 'POST',
  body: JSON.stringify({
    prompt: 'Generate user profile',
    response_json_schema: {
      type: 'object',
      properties: { name: { type: 'string' }, age: { type: 'number' } }
    }
  })
});

// Chat
fetch('${baseUrl}/api/llm/chat', {
  method: 'POST',
  body: JSON.stringify({
    messages: [{ role: 'user', content: 'Hello' }]
  })
});

// Image (saves to Vercel Blob, returns URL)
fetch('${baseUrl}/api/llm/image', {
  method: 'POST',
  body: JSON.stringify({ prompt: 'Modern app icon, blue gradient' })
});
\`\`\`

**Models (whitelisted only):**
Text: openai/gpt-4.1-nano (default), openai/gpt-4.1-mini, openai/gpt-4.1, openai/gpt-4o-mini, openai/gpt-o3-mini-high, google/gemini-2.5-flash, anthropic/claude-3.5-haiku
Image: dall-e-3 (default, single), dall-e-2 (multiple)

**Key Points:**
- Images saved to Vercel Blob, returns permanent URLs
- Validation errors return 400 with clear messages
- Image generation: 10-30s response time
- Chat supports streaming (stream: true)
- All models require whitelisting - contact support for new models
`;
  }

  /**
   * Get concise instructions for quick reference
   */
  static getQuickReference(): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://magically.life';

    return `LLM API: ${baseUrl}/api/llm/{invoke|chat|image|models} - Built-in AI with whitelisted models only`;
  }

  /**
   * Get example implementations for common use cases
   */
  static getExamples(): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://magically.life';
    
    return `
Common LLM API Use Cases:

1. **Form Validation with AI:**
\`\`\`typescript
const validateForm = async (formData) => {
  const response = await fetch('${baseUrl}/api/llm/invoke', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      prompt: \`Validate this form data: \${JSON.stringify(formData)}\`,
      response_json_schema: {
        type: 'object',
        properties: {
          isValid: { type: 'boolean' },
          errors: { type: 'array', items: { type: 'string' } },
          suggestions: { type: 'array', items: { type: 'string' } }
        }
      }
    })
  });
  return await response.json();
};
\`\`\`

2. **AI Chat Assistant:**
\`\`\`typescript
const [messages, setMessages] = useState([]);

const sendMessage = async (userMessage) => {
  const newMessages = [...messages, { role: 'user', content: userMessage }];
  
  const response = await fetch('${baseUrl}/api/llm/chat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      messages: newMessages,
      stream: false
    })
  });
  
  const data = await response.json();
  setMessages([...newMessages, data.message]);
};
\`\`\`

3. **Dynamic Image Generation:**
\`\`\`typescript
const generateAvatar = async (description) => {
  const response = await fetch('${baseUrl}/api/llm/image', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      prompt: \`Professional avatar: \${description}, clean background, portrait style\`,
      size: '1024x1024',
      quality: 'hd'
    })
  });

  const data = await response.json();
  return {
    url: data.url, // Vercel Blob URL ready for Image component
    filename: data.filename, // File path in blob storage
    downloadUrl: data.downloadUrl // Direct download link
  };
};
\`\`\`
`;
  }
}

export const llmApiService = new LLMAPIService();
