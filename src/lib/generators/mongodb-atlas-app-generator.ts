/**
 * Generate React Native app code that uses MongoDB Atlas + Device Sync + OAuth
 */

export interface MongoDBAtlasAppConfig {
  appName: string;
  atlasAppId: string;
  databaseName: string;
  oauthConfig: {
    clientId: string;
    authUrl: string;
    tokenUrl: string;
  };
  schema: Record<string, any>;
}

export function generateMongoDBAtlasApp(config: MongoDBAtlasAppConfig): Array<{name: string, content: string, language: string}> {
  const files: Array<{name: string, content: string, language: string}> = [];

  // App.tsx - Main app component
  files.push({
    name: 'App.tsx',
    content: generateAppComponent(config),
    language: 'typescript'
  });

  // realm.ts - MongoDB Realm client setup
  files.push({
    name: 'realm.ts',
    content: generateRealmClient(config),
    language: 'typescript'
  });

  // auth.tsx - OAuth authentication
  files.push({
    name: 'auth.tsx',
    content: generateAuthService(config),
    language: 'typescript'
  });

  // screens/HomeScreen.tsx
  files.push({
    name: 'screens/HomeScreen.tsx',
    content: generateHomeScreen(config),
    language: 'typescript'
  });

  // screens/LoginScreen.tsx
  files.push({
    name: 'screens/LoginScreen.tsx',
    content: generateLoginScreen(config),
    language: 'typescript'
  });

  // models/schemas.ts - Realm object schemas
  files.push({
    name: 'models/schemas.ts',
    content: generateSchemas(config),
    language: 'typescript'
  });

  // types.ts
  files.push({
    name: 'types.ts',
    content: generateTypes(config),
    language: 'typescript'
  });

  return files;
}

function generateAppComponent(config: MongoDBAtlasAppConfig): string {
  return `import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { RealmProvider } from '@realm/react';
import { AuthProvider, useAuth } from './auth';
import { realmConfig } from './realm';
import HomeScreen from './screens/HomeScreen';
import LoginScreen from './screens/LoginScreen';

const Stack = createStackNavigator();

function AppNavigator(): JSX.Element {
  const { user, loading } = useAuth();

  if (loading) {
    return <></>; // Add loading screen here
  }

  return (
    <NavigationContainer>
      <Stack.Navigator>
        {user ? (
          <Stack.Screen
            name="Home"
            component={HomeScreen}
            options={{ title: '${config.appName}' }}
          />
        ) : (
          <Stack.Screen
            name="Login"
            component={LoginScreen}
            options={{ headerShown: false }}
          />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default function App(): JSX.Element {
  return (
    <RealmProvider {...realmConfig}>
      <AuthProvider>
        <AppNavigator />
      </AuthProvider>
    </RealmProvider>
  );
}`;
}

function generateRealmClient(config: MongoDBAtlasAppConfig): string {
  return `import Realm, { BSON } from 'realm';
import { createRealmContext } from '@realm/react';
import { ${Object.keys(config.schema).map(capitalize).join(', ')} } from './models/schemas';

// MongoDB Atlas App Services configuration
const atlasAppId = '${config.atlasAppId}';
const databaseName = '${config.databaseName}';

// Create Realm app instance
export const realmApp = new Realm.App({ id: atlasAppId });

// Define the Realm configuration
export const realmConfig: Realm.Configuration = {
  schema: [${Object.keys(config.schema).map(capitalize).join(', ')}],
  sync: {
    user: realmApp.currentUser!,
    partitionValue: databaseName, // Partition by database name for perfect app isolation
    newRealmFileBehavior: {
      type: 'downloadBeforeOpen',
    },
    existingRealmFileBehavior: {
      type: 'openImmediately',
    },
  },
};

// Create Realm context
export const { RealmProvider, useRealm, useObject, useQuery } = createRealmContext(realmConfig);

// Helper function to set user for sync
export async function loginToRealm(accessToken: string) {
  try {
    // Create custom JWT credentials
    const credentials = Realm.Credentials.jwt(accessToken);
    
    // Authenticate with Realm
    const user = await realmApp.logIn(credentials);
    
    return user;
  } catch (error) {
    console.error('Failed to login to Realm:', error);
    throw error;
  }
}

// Helper function to logout from Realm
export async function logoutFromRealm() {
  try {
    if (realmApp.currentUser) {
      await realmApp.currentUser.logOut();
    }
  } catch (error) {
    console.error('Failed to logout from Realm:', error);
  }
}`;
}

function generateAuthService(config: MongoDBAtlasAppConfig): string {
  return `import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Linking } from 'react-native';
import * as WebBrowser from 'expo-web-browser';
import * as AuthSession from 'expo-auth-session';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { loginToRealm, logoutFromRealm } from './realm';
import { AuthContextType, User } from './types';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps): JSX.Element {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  const redirectUri = AuthSession.makeRedirectUri({
    useProxy: true,
  });

  useEffect(() => {
    loadStoredAuth();
  }, []);

  async function loadStoredAuth() {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      const userData = await AsyncStorage.getItem('user_data');
      
      if (token && userData) {
        // Login to Realm with stored token
        await loginToRealm(token);
        setUser(JSON.parse(userData));
      }
    } catch (error) {
      console.error('Failed to load stored auth:', error);
    } finally {
      setLoading(false);
    }
  }

  async function signIn() {
    try {
      const authUrl = \`${config.oauthConfig.authUrl}?client_id=${config.oauthConfig.clientId}&redirect_uri=\${encodeURIComponent(redirectUri)}&response_type=code&scope=read write\`;
      
      const result = await WebBrowser.openAuthSessionAsync(authUrl, redirectUri);
      
      if (result.type === 'success') {
        const { url } = result;
        const urlParams = new URLSearchParams(url.split('?')[1]);
        const code = urlParams.get('code');
        
        if (code) {
          await exchangeCodeForToken(code);
        }
      }
    } catch (error) {
      console.error('Sign in failed:', error);
      throw error;
    }
  }

  async function exchangeCodeForToken(code: string) {
    try {
      const response = await fetch('${config.oauthConfig.tokenUrl}', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          grant_type: 'authorization_code',
          code,
          client_id: '${config.oauthConfig.clientId}',
          redirect_uri: redirectUri,
        }),
      });

      const tokenData = await response.json();
      
      if (tokenData.access_token) {
        // Store token and user data
        await AsyncStorage.setItem('auth_token', tokenData.access_token);
        
        // Login to Realm with access token
        await loginToRealm(tokenData.access_token);
        
        // Decode JWT to get user info (simple decode, not verification)
        const payload = JSON.parse(atob(tokenData.access_token.split('.')[1]));
        const userData = { id: payload.sub, email: payload.email };
        
        await AsyncStorage.setItem('user_data', JSON.stringify(userData));
        setUser(userData);
      }
    } catch (error) {
      console.error('Token exchange failed:', error);
      throw error;
    }
  }

  async function signOut() {
    try {
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user_data');
      await logoutFromRealm();
      setUser(null);
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  }

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      signIn,
      signOut,
    }}>
      {children}
    </AuthContext.Provider>
  );
}`;
}

function generateHomeScreen(config: MongoDBAtlasAppConfig): string {
  const firstTable = Object.keys(config.schema)[0];
  const tableName = firstTable || 'items';

  return `import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { useQuery, useRealm } from '@realm/react';
import { ${capitalize(tableName)} } from '../models/schemas';
import { useAuth } from '../auth';

export default function HomeScreen() {
  const { user, signOut } = useAuth();
  const realm = useRealm();
  const ${tableName} = useQuery(${capitalize(tableName)});

  const handleCreate = () => {
    try {
      realm.write(() => {
        realm.create('${capitalize(tableName)}', {
          _id: new Realm.BSON.ObjectId(),
          title: \`New item \${Date.now()}\`,
          userId: user?.id,
          createdAt: new Date(),
          completed: false,
        });
      });
    } catch (error) {
      console.error('Failed to create item:', error);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Welcome to ${config.appName}</Text>
        <TouchableOpacity onPress={signOut} style={styles.signOutButton}>
          <Text style={styles.signOutText}>Sign Out</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity onPress={handleCreate} style={styles.addButton}>
        <Text style={styles.addButtonText}>Add New Item</Text>
      </TouchableOpacity>

      <FlatList
        data={${tableName}}
        keyExtractor={(item) => item._id.toString()}
        renderItem={({ item }) => (
          <View style={styles.item}>
            <Text style={styles.itemText}>{item.title || item.name || 'Item'}</Text>
            <Text style={styles.itemDate}>
              {item.createdAt?.toLocaleDateString()}
            </Text>
          </View>
        )}
        style={styles.list}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  signOutButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
  },
  signOutText: {
    color: 'white',
    fontWeight: 'bold',
  },
  addButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 20,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  list: {
    flex: 1,
  },
  item: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  itemText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  itemDate: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
});`;
}

function generateLoginScreen(config: MongoDBAtlasAppConfig): string {
  return `import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useAuth } from '../auth';

export default function LoginScreen() {
  const [loading, setLoading] = useState(false);
  const { signIn } = useAuth();

  const handleSignIn = async () => {
    try {
      setLoading(true);
      await signIn();
    } catch (error) {
      Alert.alert('Sign In Failed', 'Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>📱</Text>
        </View>
        <Text style={styles.title}>${config.appName}</Text>
        <Text style={styles.subtitle}>
          Powered by Magically.life
        </Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.welcomeText}>Welcome!</Text>
        <Text style={styles.description}>
          Sign in to access your personalized experience.
          Your data syncs in real-time across all devices.
        </Text>

        <TouchableOpacity
          onPress={handleSignIn}
          style={[styles.signInButton, loading && styles.disabled]}
          disabled={loading}
        >
          <Text style={styles.googleIcon}>🔐</Text>
          <Text style={styles.signInText}>
            {loading ? 'Signing In...' : 'Continue with Magically'}
          </Text>
        </TouchableOpacity>

        <Text style={styles.footerText}>
          Secure authentication powered by Magically.life
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#6366f1',
    paddingHorizontal: 20,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  logoText: {
    fontSize: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 30,
    paddingVertical: 40,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 40,
  },
  signInButton: {
    backgroundColor: '#1f2937',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  googleIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  signInText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  footerText: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    lineHeight: 18,
  },
  disabled: {
    opacity: 0.6,
  },
});`;
}

function generateSchemas(config: MongoDBAtlasAppConfig): string {
  const schemas = Object.keys(config.schema).map(tableName => {
    const fields = config.schema[tableName];
    const properties = Object.entries(fields).map(([key, type]) => {
      const realmType = getRealmType(type as string);
      return `    ${key}: '${realmType}',`;
    }).join('\n');

    return `export class ${capitalize(tableName)} extends Realm.Object<${capitalize(tableName)}> {
  _id!: Realm.BSON.ObjectId;
  ${Object.keys(fields).map(key => `${key}!: ${getTypeScriptType(fields[key] as string)};`).join('\n  ')}

  static schema: Realm.ObjectSchema = {
    name: '${capitalize(tableName)}',
    primaryKey: '_id',
    properties: {
      _id: 'objectId',
${properties}
    },
  };
}`;
  }).join('\n\n');

  return `import Realm from 'realm';

${schemas}`;
}

function generateTypes(config: MongoDBAtlasAppConfig): string {
  const schemaTypes = Object.keys(config.schema).map(tableName => {
    const fields = config.schema[tableName];
    const fieldTypes = Object.entries(fields).map(([key, type]) => {
      const tsType = getTypeScriptType(type as string);
      return `  ${key}: ${tsType};`;
    }).join('\n');

    return `export interface ${capitalize(tableName)}Type {
  _id: Realm.BSON.ObjectId;
${fieldTypes}
}`;
  }).join('\n\n');

  return `// Generated types for ${config.appName}
import Realm from 'realm';

export interface User {
  id: string;
  email: string;
  name?: string;
}

export interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: () => Promise<void>;
  signOut: () => Promise<void>;
}

${schemaTypes}
`;
}

function getRealmType(type: string): string {
  switch (type) {
    case 'string': return 'string';
    case 'number': return 'int';
    case 'boolean': return 'bool';
    case 'date': return 'date';
    case 'array': return 'list';
    default: return 'string';
  }
}

function getTypeScriptType(type: string): string {
  switch (type) {
    case 'string': return 'string';
    case 'number': return 'number';
    case 'boolean': return 'boolean';
    case 'date': return 'Date';
    case 'array': return 'any[]';
    default: return 'string';
  }
}

function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
