import { FileItem } from '@/types/file';
import path from 'path';
import { getProjectById } from '@/lib/db/project-queries';
import { decrypt } from '@/lib/utils/encryption';

/**
 * Handles the processing of Deno Subhosting artifacts (functions)
 * created by the AI in the chat flow
 */
export class DenoArtifactHandler {
    private accessToken: string;
    private orgId: string;
    private baseUrl = 'https://api.deno.com/v1';

    constructor() {
        this.accessToken = process.env.DEPLOY_ACCESS_TOKEN!;
        this.orgId = process.env.DEPLOY_ORG_ID!;

        if (!this.accessToken || !this.orgId) {
            throw new Error('DEPLOY_ACCESS_TOKEN and DEPLOY_ORG_ID environment variables are required');
        }
    }

    /**
     * Make authenticated request to Deno Deploy API
     */
    private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
        const url = `${this.baseUrl}${endpoint}`;
        const response = await fetch(url, {
            ...options,
            headers: {
                'Authorization': `Bearer ${this.accessToken}`,
                'Content-Type': 'application/json',
                ...options.headers,
            },
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Deno Deploy API error (${response.status}): ${errorText}`);
        }

        return response.json();
    }

    /**
     * Process function files - deploy them to Deno Subhosting
     * Only deploys functions that have been modified (based on dirty file paths)
     * @param files List of all files from the file manager
     * @param projectId Project ID for environment variables
     * @param dataStream Stream to send notifications to the client
     * @param dirtyFilePaths List of file paths that have been modified
     */
    async processFunctions(
        files: FileItem[], 
        projectId: string,
        dataStream?: { writeData: (data: any) => void },
        dirtyFilePaths?: string[] // Paths of files that have been modified
    ): Promise<void> {
        // If no dirty file paths provided or empty, nothing to deploy
        if (!dirtyFilePaths || dirtyFilePaths.length === 0) {
            console.log('No modified files to process');
            return;
        }
        
        // Filter function files based on path pattern
        const allFunctionFiles = files.filter(file => {
            // Check if the file path matches the Deno function pattern
            return file.name.includes('magically/functions/') && 
                   (file.name.endsWith('.ts') || file.name.endsWith('.js'));
        });

        if (allFunctionFiles.length === 0) {
            console.log('No Deno function files found');
            return;
        }
        
        // Only process functions that have modified files
        const modifiedFunctionFiles = allFunctionFiles.filter(file => 
            dirtyFilePaths.some(dirtyPath => dirtyPath === file.name)
        );
            
        if (modifiedFunctionFiles.length === 0) {
            console.log('No modified Deno function files found');
            return;
        }
        
        console.log(`Found ${modifiedFunctionFiles.length} modified Deno function files out of ${allFunctionFiles.length} total function files`);
        
        // Group functions by their directory (function name)
        const functionsByName = new Map<string, FileItem[]>();

        for (const file of modifiedFunctionFiles) {
            // Expected path format: magically/functions/function-name/index.ts
            const pathParts = file.name.split('/');
            const functionDirIndex = pathParts.findIndex((part: string) => part === 'functions') + 1;
            
            if (functionDirIndex <= 0 || functionDirIndex >= pathParts.length) {
                console.warn(`Invalid function path: ${file.name}. Expected format: magically/functions/function-name/index.ts`);
                continue;
            }
            
            const functionName = pathParts[functionDirIndex];
            
            // For each modified function file, we need to get all files for that function
            // Even if only one file in a function was modified, we need to deploy the entire function
            const functionAllFiles = files.filter(f => {
                const parts = f.name.split('/');
                const idx = parts.findIndex((part: string) => part === 'functions') + 1;
                return idx > 0 && idx < parts.length && parts[idx] === functionName;
            });
            
            if (!functionsByName.has(functionName)) {
                functionsByName.set(functionName, functionAllFiles);
            }
        }
        
        // Process each function
        for (const [functionName, functionFiles] of functionsByName.entries()) {
            try {
                console.log(`Deploying modified function: ${functionName}`);
                
                // Find the main function file (index.ts)
                const mainFile = functionFiles.find(file => {
                    const fileName = path.basename(file.name);
                    return fileName === 'index.ts';
                });
                
                if (!mainFile) {
                    console.warn(`No index.ts file found for function: ${functionName}`);
                    continue;
                }
                
                // Prepare files for deployment
                const deployFiles = functionFiles.map(file => {
                    // Extract the relative path within the function directory
                    const pathParts = file.name.split('/');
                    const functionDirIndex = pathParts.findIndex((part: string) => part === 'functions') + 1;
                    const relativePath = pathParts.slice(functionDirIndex + 1).join('/');
                    
                    return {
                        name: relativePath,
                        content: file.content
                    };
                });
                
                // Deploy the function to Deno Subhosting
                const result = await this.deployFunction({
                    functionName,
                    files: deployFiles,
                    projectId
                });
                
                console.log(`Deployed function: ${functionName}`, result);
                
                // Notify client
                dataStream?.writeData({
                    type: 'function-operation',
                    content: {
                        type: result.deployment ? 'update' : 'create',
                        name: functionName,
                        path: mainFile.name,
                        url: result.url,
                        result
                    }
                });
            } catch (error) {
                console.error(`Error processing function ${functionName}:`, error);
                
                // Notify client of error
                dataStream?.writeData({
                    type: 'function-operation',
                    content: {
                        type: 'error',
                        name: functionName,
                        error: error instanceof Error ? error.message : String(error)
                    }
                });
            }
        }
    }

    /**
     * Deploy a function to Deno Subhosting
     * @param functionName Name of the function
     * @param files Files to deploy
     * @param projectId Project ID for environment variables
     */
    private async deployFunction({
        functionName,
        files,
        projectId
    }: {
        functionName: string;
        files: Array<{ name: string; content: string }>;
        projectId: string;
    }): Promise<any> {
        try {
            // Create a valid Deno Deploy project name
            // Rules: 3-26 chars, a-z 0-9 and -, no start/end with -, no 8 or 12 char segments after -
            const sanitizedProjectId = projectId.replace(/[^a-z0-9]/g, '').substring(0, 8);
            const sanitizedFunctionName = functionName.replace(/[^a-z0-9]/g, '').substring(0, 10);
            const projectName = `mg-${sanitizedProjectId}-${sanitizedFunctionName}`;

            console.log(`🏷️  Creating project with name: ${projectName} (original: magically-${projectId}-${functionName})`);

            // Create or get existing project
            let project;
            try {
                // Try to get existing project
                const projects = await this.request<any[]>(`/organizations/${this.orgId}/projects`);
                project = projects.find(p => p.name === projectName);
            } catch (error) {
                console.log('No existing project found, will create new one');
            }

            if (!project) {
                // Create new project
                project = await this.request<any>(`/organizations/${this.orgId}/projects`, {
                    method: 'POST',
                    body: JSON.stringify({
                        name: projectName,
                        description: `Magically generated function: ${functionName}`
                    })
                });
            }

            // Get project-specific MongoDB credentials
            let mongodbUri = process.env.MONGODB_URI || '';
            try {
                const projectData = await getProjectById(projectId);
                if (projectData?.mongodbConnectionString) {
                    mongodbUri = projectData.mongodbConnectionString;
                    console.log(`🔗 Using project-specific MongoDB connection for: ${projectId}`);
                } else {
                    console.log(`⚠️ No project-specific MongoDB credentials found, using fallback`);
                }
            } catch (error) {
                console.error('❌ Failed to get project MongoDB credentials:', error);
                console.log('🔄 Falling back to default MongoDB URI');
            }

            // Prepare environment variables
            const envVars = {
                MONGODB_URI: mongodbUri,
                APP_ID: projectId,
                FUNCTION_NAME: functionName,
                // Add other environment variables that might be needed
                SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
                SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY || ''
            };

            // Prepare assets
            const assets: Record<string, any> = {};
            files.forEach(file => {
                assets[file.name] = {
                    kind: 'file',
                    content: file.content,
                    encoding: 'utf-8'
                };
            });

            console.log('envVars', envVars)
            // Create deployment
            const deployment = await this.request<any>(`/projects/${project.id}/deployments`, {
                method: 'POST',
                body: JSON.stringify({
                    entryPointUrl: 'index.ts',
                    assets,
                    envVars
                })
            });

            console.log('deployment', deployment)

            // Get the deployment URL
            let url;
            try {
                const domains = await this.request<any[]>(`/projects/${project.id}/domains`);
                url = domains.length > 0 ? `https://${domains[0].domain}` : `https://${project.id}.deno.dev`;
            } catch (error) {
                // Fallback URL if domains endpoint fails
                url = `https://${project.id}.deno.dev`;
            }

            return {
                project,
                deployment,
                url
            };
        } catch (error) {
            console.error('Error deploying to Deno Subhosting:', error);
            return {
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
}
