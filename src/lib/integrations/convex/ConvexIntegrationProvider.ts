import { BaseIntegrationProvider } from '../base/BaseIntegrationProvider';
import { IntegrationConfig, IntegrationMetadata } from '../base/types';
import { db } from '@/lib/db/db';
import { projects, oauthClients } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { nanoid } from 'nanoid';

export interface ConvexProject {
  id: string;
  name: string;
  deploymentUrl: string;
  deploymentKey: string;
  team: string;
}

export class ConvexIntegrationProvider extends BaseIntegrationProvider {
  constructor() {
    const config: IntegrationConfig = {
      apiKey: {
        required: true,
        fields: [
          {
            key: 'apiToken',
            label: 'Convex API Token',
            type: 'password',
            required: true,
          },
          {
            key: 'team',
            label: 'Team Name',
            type: 'text',
            required: true,
          },
        ],
      },
    };
    super('convex', config);
  }

  async validateCredentials(metadata: IntegrationMetadata): Promise<boolean> {
    try {
      if (!metadata.credentials?.apiToken) {
        return false;
      }

      // Test API token by making a simple request
      const response = await fetch('https://api.convex.dev/api/projects', {
        headers: {
          'Authorization': `Bearer ${metadata.credentials.apiToken}`,
          'Content-Type': 'application/json',
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to validate Convex credentials:', error);
      return false;
    }
  }

  /**
   * Create a new Convex project for a user's app
   */
  async createProject({
    userId,
    appName,
    projectId,
  }: {
    userId: string;
    appName: string;
    projectId: string;
  }): Promise<ConvexProject> {
    try {
      // Get user's Convex connection (admin setup)
      const connection = await this.getConnectionByUserId(userId);
      if (!connection?.metadata?.credentials?.apiToken) {
        throw new Error('No Convex connection found for user');
      }

      const { apiToken, team } = connection.metadata.credentials;
      const projectName = `${appName.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${nanoid(8)}`;

      // Create Convex project via API
      const response = await fetch('https://api.convex.dev/api/create_project', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiToken}`,
        },
        body: JSON.stringify({
          team,
          projectName,
          deploymentType: 'dev', // Start with dev, can upgrade to prod later
        }),
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(`Failed to create Convex project: ${error}`);
      }

      const convexProject = await response.json();

      // Create OAuth client for this app
      const clientId = nanoid(32);
      const clientSecret = nanoid(64);

      await db.insert(oauthClients).values({
        clientId,
        clientSecret,
        appId: projectId,
        redirectUri: `${process.env.NEXT_PUBLIC_APP_URL}/projects/${projectId}/callback/magically`, // Centralized OAuth callback
        name: appName,
      });

      // Update project with Convex details
      await db.update(projects)
        .set({
          convexProjectId: convexProject.id,
          convexDeploymentUrl: convexProject.deploymentUrl,
          convexDeploymentKey: convexProject.deploymentKey,
          convexTeam: team,
        })
        .where(eq(projects.id, projectId));

      return {
        id: convexProject.id,
        name: projectName,
        deploymentUrl: convexProject.deploymentUrl,
        deploymentKey: convexProject.deploymentKey,
        team,
      };
    } catch (error) {
      console.error('Failed to create Convex project:', error);
      throw error;
    }
  }

  /**
   * Deploy schema and functions to Convex project
   */
  async deployToProject({
    projectId,
    schema,
    functions,
  }: {
    projectId: string;
    schema: Record<string, any>;
    functions: Record<string, string>;
  }): Promise<void> {
    try {
      // Get project details
      const [project] = await db
        .select()
        .from(projects)
        .where(eq(projects.id, projectId))
        .limit(1);

      if (!project) {
        throw new Error('Project not found');
      }

      // TODO: Implement Convex deployment
      // This would involve:
      // 1. Creating schema.ts file
      // 2. Creating function files
      // 3. Deploying via Convex CLI or API
      
      console.log('Deploying to Convex project:', {
        projectId,
        schema: Object.keys(schema),
        functions: Object.keys(functions),
      });
    } catch (error) {
      console.error('Failed to deploy to Convex project:', error);
      throw error;
    }
  }

  /**
   * Get OAuth configuration for an app
   */
  async getOAuthConfig(appId: string): Promise<{
    clientId: string;
    authUrl: string;
    tokenUrl: string;
  }> {
    try {
      const [client] = await db
        .select()
        .from(oauthClients)
        .where(eq(oauthClients.appId, appId))
        .limit(1);

      if (!client) {
        throw new Error('OAuth client not found for app');
      }

      const baseUrl = process.env.NEXT_PUBLIC_APP_URL;

      return {
        clientId: client.clientId,
        authUrl: `${baseUrl}/api/oauth/authorize`,
        tokenUrl: `${baseUrl}/api/oauth/token`,
      };
    } catch (error) {
      console.error('Failed to get OAuth config:', error);
      throw error;
    }
  }

  private async getConnectionByUserId(userId: string) {
    // This would get the admin's Convex connection
    // For now, we'll assume there's a system-wide Convex connection
    // In production, you'd have admin credentials configured
    return {
      metadata: {
        credentials: {
          apiToken: process.env.CONVEX_API_TOKEN,
          team: process.env.CONVEX_TEAM,
        },
      },
    };
  }
}
