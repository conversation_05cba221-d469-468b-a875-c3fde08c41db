/**
 * InstantDB Platform API Client
 * Wrapper around InstantDB Platform APIs for app management
 */

import { getInstantDbPlatformConfig } from '@/lib/db/instantdb-queries';

export interface InstantDBApp {
  id: string;
  title: string;
  creator_id: string;
  created_at: string;
}

export interface InstantDBSchema {
  entities: {
    [namespace: string]: {
      attrs: {
        [attribute: string]: {
          valueType: 'string' | 'number' | 'boolean' | 'date' | 'json';
          config: {
            indexed?: boolean;
            unique?: boolean;
          };
        };
      };
    };
  };
  links: {
    [linkId: string]: {
      forward: {
        on: string;
        label: string;
        has: 'many' | 'one';
        onDelete?: 'cascade' | 'null';
      };
      reverse: {
        on: string;
        label: string;
        has: 'many' | 'one';
        onDelete?: 'cascade' | 'null';
      };
    };
  };
}

export class InstantDBClient {
  private baseUrl = 'https://api.instantdb.com';
  private accessToken: string | null = null;

  constructor(accessToken?: string) {
    this.accessToken = accessToken || null;
  }

  /**
   * Initialize client with stored platform tokens
   */
  static async create(): Promise<InstantDBClient> {
    const config = await getInstantDbPlatformConfig();
    if (!config?.accessToken) {
      throw new Error('InstantDB platform not configured or no access token available');
    }
    return new InstantDBClient(config.accessToken);
  }

  /**
   * Make authenticated request to InstantDB API
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    const url = `${this.baseUrl}${endpoint}`;

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
          ...options.headers,
        },
        // Add timeout to detect service issues
        signal: AbortSignal.timeout(30000), // 30 second timeout
      });

      if (!response.ok) {
        const errorText = await response.text();

        // Check for service availability issues
        if (response.status >= 500 && response.status < 600) {
          throw new Error(`InstantDB service is temporarily unavailable (${response.status})`);
        }

        throw new Error(`InstantDB API error (${response.status}): ${errorText}`);
      }

      return response.json();
    } catch (error) {
      // Handle network/timeout errors
      if (error instanceof Error) {
        if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
          throw new Error('InstantDB service is not responding (timeout)');
        }
        if (error.message.includes('fetch')) {
          throw new Error('Unable to connect to InstantDB service');
        }
      }
      throw error;
    }
  }

  /**
   * Create a new InstantDB app
   */
  async createApp(title: string): Promise<InstantDBApp> {
    const response = await this.request<{ app: InstantDBApp }>('/superadmin/apps', {
      method: 'POST',
      body: JSON.stringify({ title }),
    });
    return response.app;
  }

  /**
   * Delete an InstantDB app
   */
  async deleteApp(appId: string): Promise<InstantDBApp> {
    const response = await this.request<{ app: InstantDBApp }>(`/superadmin/apps/${appId}`, {
      method: 'DELETE',
    });
    return response.app;
  }

  /**
   * Get app schema
   */
  async getSchema(appId: string): Promise<any> {
    const response = await this.request<{ schema: any }>(`/superadmin/apps/${appId}/schema`);
    return response.schema;
  }

  /**
   * Plan schema changes (preview without applying)
   */
  async planSchemaPush(appId: string, schema: InstantDBSchema): Promise<any> {
    const response = await this.request<any>(`/superadmin/apps/${appId}/schema/push/plan`, {
      method: 'POST',
      body: JSON.stringify({ schema }),
    });
    return response;
  }

  /**
   * Apply schema changes
   */
  async applySchemaPush(appId: string, schema: InstantDBSchema): Promise<any> {
    const response = await this.request<any>(`/superadmin/apps/${appId}/schema/push/apply`, {
      method: 'POST',
      body: JSON.stringify({ schema }),
    });
    return response;
  }

  /**
   * List all apps (for debugging/admin purposes)
   */
  async listApps(): Promise<InstantDBApp[]> {
    const response = await this.request<InstantDBApp[]>('/superadmin/apps');
    return response;
  }
}

/**
 * Helper function to create InstantDB client
 */
export async function createInstantDBClient(): Promise<InstantDBClient> {
  return InstantDBClient.create();
}

/**
 * Generate basic schema for common app types
 */
export function generateBasicSchema(appType: string): InstantDBSchema {
  const baseSchema: InstantDBSchema = {
    entities: {},
    links: {},
  };

  switch (appType) {
    case 'todo':
      baseSchema.entities.todos = {
        attrs: {
          title: { valueType: 'string', config: { indexed: true } },
          completed: { valueType: 'boolean', config: {} },
          description: { valueType: 'string', config: {} },
          createdAt: { valueType: 'date', config: { indexed: true } },
          updatedAt: { valueType: 'date', config: {} },
        },
      };
      break;

    case 'chat':
      baseSchema.entities.messages = {
        attrs: {
          content: { valueType: 'string', config: {} },
          sender: { valueType: 'string', config: { indexed: true } },
          createdAt: { valueType: 'date', config: { indexed: true } },
        },
      };
      break;

    case 'notes':
      baseSchema.entities.notes = {
        attrs: {
          title: { valueType: 'string', config: { indexed: true } },
          content: { valueType: 'string', config: {} },
          tags: { valueType: 'json', config: {} },
          createdAt: { valueType: 'date', config: { indexed: true } },
          updatedAt: { valueType: 'date', config: {} },
        },
      };
      break;

    default:
      // Generic schema
      baseSchema.entities.items = {
        attrs: {
          name: { valueType: 'string', config: { indexed: true } },
          description: { valueType: 'string', config: {} },
          createdAt: { valueType: 'date', config: { indexed: true } },
        },
      };
  }

  return baseSchema;
}
