import { FileItem } from '@/types/file';
import path from 'path';
import fs from 'fs/promises';
import { SupabaseIntegrationProvider } from './SupabaseIntegrationProvider';
import { CreateFunctionRequestBody, CreateSecretsRequestBody } from 'supabase-management-js';

/**
 * Handles the processing of Supabase artifacts (functions and secrets)
 * created by the AI in the chat flow
 */
export class SupabaseArtifactHandler {
    constructor(private supabaseProvider: SupabaseIntegrationProvider) {}

    /**
     * Process function files - save them to the file system and deploy to Supabase
     * @param files List of all files from the file manager
     * @param connectionId Supabase connection ID
     * @param projectRef Supabase project reference
     * @param projectPath Local project path
     * @param dataStream Stream to send notifications to the client
     */


    /**
     * Process function files - save them to the file system and deploy to Supabase
     * Only deploys functions that have been modified (based on dirty file paths)
     * @param files List of all files from the file manager
     * @param connectionId Supabase connection ID
     * @param projectRef Supabase project reference
     * @param projectPath Local project path
     * @param dataStream Stream to send notifications to the client
     * @param dirtyFilePaths List of file paths that have been modified
     */
    async processFunctions(
        files: FileItem[], 
        connectionId: string, 
        projectRef: string, 
        projectPath: string,
        dataStream?: { writeData: (data: any) => void },
        dirtyFilePaths?: string[] // Paths of files that have been modified
    ): Promise<void> {
        // If no dirty file paths provided or empty, nothing to deploy
        if (!dirtyFilePaths || dirtyFilePaths.length === 0) {
            console.log('No modified files to process');
            return;
        }
        
        // Filter function files based on path pattern
        const allFunctionFiles = files.filter(file => {
            // Check if the file path matches the Supabase function pattern
            // Include both regular function files and shared files
            return file.name.includes('supabase/functions/') && 
                   (file.name.endsWith('.ts') || file.name.endsWith('.js'));
        });

        if (allFunctionFiles.length === 0) {
            console.log('No Supabase function files found');
            return;
        }
        
        // Only process functions that have modified files
        const modifiedFunctionFiles = allFunctionFiles.filter(file => 
            dirtyFilePaths.some(dirtyPath => dirtyPath === file.name)
        );
            
        if (modifiedFunctionFiles.length === 0) {
            console.log('No modified Supabase function files found');
            return;
        }
        
        console.log(`Found ${modifiedFunctionFiles.length} modified Supabase function files out of ${allFunctionFiles.length} total function files`);
        
        // Find shared files
        const sharedFiles = files.filter(file => {
            return file.name.includes('supabase/functions/_shared/');
        });
        
        // Group functions by their directory (function name)
        const functionsByName = new Map<string, FileItem[]>();

        for (const file of modifiedFunctionFiles) {
            // Skip shared files for now, we'll handle them separately
            if (file.name.includes('/_shared/')) {
                continue;
            }
            
            // Expected path format: supabase/functions/function-name/index.ts
            const pathParts = file.name.split('/');
            const functionDirIndex = pathParts.findIndex((part: string) => part === 'functions') + 1;
            
            if (functionDirIndex <= 0 || functionDirIndex >= pathParts.length) {
                console.warn(`Invalid function path: ${file.name}. Expected format: supabase/functions/function-name/index.ts`);
                continue;
            }
            
            const functionName = pathParts[functionDirIndex];
            
            // Skip _shared directory as a function name
            if (functionName === '_shared') {
                continue;
            }
            
            // For each modified function file, we need to get all files for that function
            // Even if only one file in a function was modified, we need to deploy the entire function
            const functionAllFiles = files.filter(f => {
                const parts = f.name.split('/');
                const idx = parts.findIndex((part: string) => part === 'functions') + 1;
                return idx > 0 && idx < parts.length && parts[idx] === functionName;
            });
            
            if (!functionsByName.has(functionName)) {
                functionsByName.set(functionName, functionAllFiles);
            }
        }
        
        // If any shared files were modified, we need to redeploy all functions
        const modifiedSharedFiles = sharedFiles.filter(file => 
            dirtyFilePaths.some(dirtyPath => dirtyPath === file.name)
        );
        
        if (modifiedSharedFiles.length > 0) {
            console.log(`Found ${modifiedSharedFiles.length} modified shared files. Will include them in all function deployments.`);
            
            // Find all function directories (excluding _shared)
            const allFunctionDirs = new Set<string>();
            
            for (const file of allFunctionFiles) {
                const pathParts = file.name.split('/');
                const functionDirIndex = pathParts.findIndex((part: string) => part === 'functions') + 1;
                
                if (functionDirIndex > 0 && functionDirIndex < pathParts.length) {
                    const functionName = pathParts[functionDirIndex];
                    if (functionName !== '_shared') {
                        allFunctionDirs.add(functionName);
                    }
                }
            }
            
            // Make sure all functions are in the map, even if they weren't modified
            for (const functionName of allFunctionDirs) {
                if (!functionsByName.has(functionName)) {
                    const functionAllFiles = files.filter(f => {
                        const parts = f.name.split('/');
                        const idx = parts.findIndex((part: string) => part === 'functions') + 1;
                        return idx > 0 && idx < parts.length && parts[idx] === functionName;
                    });
                    
                    functionsByName.set(functionName, functionAllFiles);
                }
            }
        }
        
        // Process each function
        for (const [functionName, functionFiles] of functionsByName.entries()) {
            // Skip _shared as a function name
            if (functionName === '_shared') {
                continue;
            }
            
            try {
                // We're only processing functions that have modified files
                console.log(`Deploying modified function: ${functionName}`);
                
                // Find the main function file (index.ts)
                const mainFile = functionFiles.find(file => {
                    const fileName = path.basename(file.name);
                    return fileName === 'index.ts';
                });
                
                if (!mainFile) {
                    console.warn(`No index.ts file found for function: ${functionName}`);
                    continue;
                }
                
                // Prepare files for deployment
                let deployFiles = functionFiles.map(file => {
                    // Extract the relative path within the function directory
                    const pathParts = file.name.split('/');
                    const functionDirIndex = pathParts.findIndex((part: string) => part === 'functions') + 1;
                    const relativePath = pathParts.slice(functionDirIndex + 1).join('/');
                    
                    return {
                        name: relativePath,
                        content: file.content
                    };
                });
                
                // Add shared files to the deployment if they exist
                if (sharedFiles.length > 0) {
                    // First, check if any function files are importing from the _shared directory
                    const needsSharedFiles = functionFiles.some(file => {
                        return file.content.includes('../_shared/') || file.content.includes('./_shared/');
                    });
                    
                    if (needsSharedFiles) {
                        console.log(`Function ${functionName} imports from _shared directory, including shared files`);
                        
                        // Process each shared file
                        const sharedDeployFiles = sharedFiles.map(file => {
                            // Extract the filename from the shared file path
                            const fileName = path.basename(file.name);
                            
                            // Create a modified version of the file content with fixed imports
                            let fileContent = file.content;
                            
                            // Add the shared file directly to the function directory
                            // This way, imports like '../_shared/cors.ts' will be fixed
                            return {
                                name: fileName,
                                content: fileContent
                            };
                        });
                        
                        // Combine function files with shared files
                        deployFiles = [...deployFiles, ...sharedDeployFiles];
                        
                        // Now, update import statements in all function files
                        deployFiles = deployFiles.map(file => {
                            if (file.name.endsWith('.ts') || file.name.endsWith('.js')) {
                                // Process each line of the file to properly handle imports
                                const lines = file.content.split('\n');
                                const updatedLines = lines.map(line => {
                                    // Only modify lines that import from _shared
                                    if (line.includes('../_shared/') && line.includes('import ')) {
                                        return line.replace('../_shared/', './');
                                    } else if (line.includes('./_shared/') && line.includes('import ')) {
                                        return line.replace('./_shared/', './');
                                    }
                                    return line;
                                });
                                
                                return {
                                    ...file,
                                    content: updatedLines.join('\n')
                                };
                            }
                            return file;
                        });
                        
                        // Log the updated files for debugging
                        console.log(`Updated import statements for ${functionName} function files`);
                    }
                }
                
                // Check if verify_jwt is specified in the file content
                const verifyJwt = mainFile.content.includes('// @verify_jwt: true');
                
                // Prepare metadata for deployment
                const metadata = {
                    name: functionName,
                    entrypoint_path: 'index.ts',
                    verify_jwt: verifyJwt
                };
                
                // Deploy the function using the new API
                const result = await this.supabaseProvider.deployFunction({
                    connectionId,
                    projectRef,
                    slug: functionName,
                    files: deployFiles,
                    metadata
                });
                
                console.log(`Deployed function: ${functionName}`, result);
                
                // Notify client
                dataStream?.writeData({
                    type: 'function-operation',
                    content: {
                        type: result.id ? 'update' : 'create',
                        name: functionName,
                        path: mainFile.name,
                        result
                    }
                });
            } catch (error) {
                console.error(`Error processing function ${functionName}:`, error);
                
                // Notify client of error
                dataStream?.writeData({
                    type: 'function-operation',
                    content: {
                        type: 'error',
                        name: functionName,
                        error: error instanceof Error ? error.message : String(error)
                    }
                });
            }
        }
    }
    
    /**
     * Save function files to the local file system
     * @param files Function files to save
     * @param projectPath Local project path
     */
    private async saveFunctionFiles(files: FileItem[], projectPath: string): Promise<void> {
        for (const file of files) {
            try {
                // Ensure the file path is absolute
                const absolutePath = path.isAbsolute(file.name) 
                    ? file.name 
                    : path.join(projectPath, file.name);
                
                // Create directory if it doesn't exist
                const directory = path.dirname(absolutePath);
                await fs.mkdir(directory, { recursive: true });
                
                // Write file content
                await fs.writeFile(absolutePath, file.content);
                console.log(`Saved function file: ${file.name}`);
                
                // If this is an index.ts file, also create a deno.json file if it doesn't exist
                if (path.basename(file.name) === 'index.ts') {
                    const denoJsonPath = path.join(directory, 'deno.json');
                    
                    // Check if deno.json already exists
                    try {
                        await fs.access(denoJsonPath);
                    } catch (error) {
                        // File doesn't exist, create it
                        const denoJsonContent = JSON.stringify({
                            "imports": {
                                "std/": "https://deno.land/std@0.177.0/",
                                "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@2.21.0"
                            },
                            "tasks": {
                                "dev": "deno run --allow-net --allow-env --allow-read index.ts"
                            }
                        }, null, 2);
                        
                        await fs.writeFile(denoJsonPath, denoJsonContent, 'utf8');
                    }
                }
            } catch (error) {
                console.error(`Error saving function file ${file.name}:`, error);
                throw error;
            }
        }
    }
    
    /**
     * Process secret files - extract secret names and notify the client
     * @param files List of all files from the file manager
     * @param dataStream Stream to send notifications to the client
     */
    async processSecrets(
        files: FileItem[],
        dataStream?: { writeData: (data: any) => void }
    ): Promise<void> {
        // Filter secret files based on path pattern
        const secretFiles = files.filter(file => {
            // Check if the file path matches the secrets pattern
            return file.name.includes('secrets/') && file.name.endsWith('.json');
        });
        
        if (secretFiles.length === 0) {
            return;
        }
        
        for (const file of secretFiles) {
            try {
                // Parse the secret file content
                const secretData = JSON.parse(file.content);
                
                // Extract secret name from file path
                // Expected format: supabase/secrets/secret-name.json
                const fileName = path.basename(file.name, '.json');
                
                // Determine scope and environment from the content or defaults
                const scope = secretData.scope || 'edge_functions';
                const environment = secretData.environment || 'all';
                
                // Notify client to prompt for secret value
                dataStream?.writeData({
                    type: 'secret-prompt',
                    content: {
                        name: fileName,
                        description: secretData.description || '',
                        path: file.name,
                        scope,
                        environment
                    }
                });
                
                console.log(`Processed secret file: ${file.name}`);
            } catch (error) {
                console.error(`Error processing secret file ${file.name}:`, error);
                
                // Notify client of error
                dataStream?.writeData({
                    type: 'secret-operation',
                    content: {
                        type: 'error',
                        name: path.basename(file.name, '.json'),
                        error: error instanceof Error ? error.message : String(error)
                    }
                });
            }
        }
    }
    
    /**
     * Deploy secrets to Supabase
     * @param connectionId Supabase connection ID
     * @param projectRef Supabase project reference
     * @param secrets Secret key-value pairs to deploy
     */
    async deploySecrets(
        connectionId: string,
        projectRef: string,
        secrets: Record<string, string>,
        dataStream?: { writeData: (data: any) => void }
    ): Promise<void> {
        try {
            // Format secrets for Supabase API
            const secretsBody: CreateSecretsRequestBody = Object.entries(secrets).map(([name, value]) => ({
                name,
                value
            }));
            
            // Deploy secrets
            await this.supabaseProvider.createSecrets({
                connectionId,
                projectRef,
                secrets: secretsBody
            });
            
            console.log(`Deployed ${Object.keys(secrets).length} secrets to project ${projectRef}`);
            
            // Notify client
            dataStream?.writeData({
                type: 'secret-operation',
                content: {
                    type: 'create',
                    count: Object.keys(secrets).length
                }
            });
        } catch (error) {
            console.error(`Error deploying secrets:`, error);
            
            // Notify client of error
            dataStream?.writeData({
                type: 'secret-operation',
                content: {
                    type: 'error',
                    error: error instanceof Error ? error.message : String(error)
                }
            });
            
            throw error;
        }
    }
}
