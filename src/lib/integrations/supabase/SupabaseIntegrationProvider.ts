import {createClient} from '@supabase/supabase-js';
import {BaseIntegrationProvider} from '../base/BaseIntegrationProvider';
import {IntegrationConfig, IntegrationMetadata, OAuthToken} from '../base/types';
import {SupabaseMetadata} from './types';
import {
    isSupabaseError,
    SupabaseManagementAPI,
    CreateFunctionRequestBody,
    CreateFunctionResponseData,
    UpdateFunctionRequestBody,
    UpdateFunctionResponseData,
    CreateSecretsRequestBody,
    DeleteSecretsRequestBody,
    DeleteSecretsResponseData, UpdateProjectAuthConfigRequestBody
} from "supabase-management-js";
import { exec } from 'child_process';
import { promisify } from 'util';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { Chat, chat, Connection, connection, Project, projects } from "@/lib/db/schema";
import { and, eq } from "drizzle-orm";
import { db } from '@/lib/db/db';
import {SupabaseDebuggingTools} from "@/lib/integrations/supabase/SupabaseDebuggingTools";

// Custom error classes for better error handling
export class SupabaseIntegrationError extends Error {
    constructor(message: string, public readonly context: Record<string, any> = {}) {
        super(message);
        this.name = 'SupabaseIntegrationError';
    }
}

export class SupabaseTokenError extends SupabaseIntegrationError {
    constructor(message: string, context: Record<string, any> = {}) {
        super(message, context);
        this.name = 'SupabaseTokenError';
    }
}

export class SupabaseConnectionError extends SupabaseIntegrationError {
    constructor(message: string, context: Record<string, any> = {}) {
        super(message, context);
        this.name = 'SupabaseConnectionError';
    }
}

const execAsync = promisify(exec);

export class SupabaseIntegrationProvider extends BaseIntegrationProvider {
    // Instance of the debugging tools
    private debuggingTools: SupabaseDebuggingTools = new SupabaseDebuggingTools();
    // Store refresh token promises to prevent race conditions
    private refreshTokenPromises: Record<string, Promise<OAuthToken>> = {};

    // Helper method to check if a token needs refreshing
    private shouldRefreshToken(oauth: OAuthToken): boolean {
        if (!oauth.expiresAt) return true;

        const expiresAt = new Date(oauth.expiresAt).getTime();
        const now = Date.now();
        const fiveMinutesInMs = 5 * 60 * 1000;

        // Refresh if token expires in less than 5 minutes
        return now + fiveMinutesInMs >= expiresAt;
    }
    getAuthUrl(): string {
        if (!this.config.oauth) {
            throw new Error('OAuth is not configured for this provider');
        }

        const {authorizationUrl, clientId, redirectUri, scope} = this.config.oauth;
        const params = new URLSearchParams({
            client_id: clientId,
            redirect_uri: redirectUri,
            response_type: 'code',
            scope: scope.join(','), // Supabase uses comma-separated scopes
        });

        return `${authorizationUrl}?${params.toString()}`;
    }

    constructor() {
        const config: IntegrationConfig = {
            oauth: {
                clientId: process.env.SUPABASE_CLIENT_ID!,
                clientSecret: process.env.SUPABASE_CLIENT_SECRET!,
                redirectUri: `${process.env.NEXT_PUBLIC_APP_URL}/api/integrations/supabase/callback`,
                scope: [
                    'database:read',     // Read database configs and schemas
                    'rest:read',         // Read API configurations
                    'projects:read',     // Read project metadata
                    'storage:read'       // Read storage configurations
                ],
                authorizationUrl: 'https://api.supabase.com/v1/oauth/authorize',
                tokenUrl: 'https://api.supabase.com/v1/oauth/token',
            },
            apiKey: {
                required: true,
                fields: [
                    {
                        key: 'projectUrl',
                        label: 'Project URL',
                        type: 'text',
                        required: true,
                    },
                    {
                        key: 'apiKey',
                        label: 'API Key',
                        type: 'password',
                        required: true,
                    },
                ],
            },
        };
        super('supabase', config);
    }

    async validateCredentials(metadata: IntegrationMetadata): Promise<boolean> {
        try {
            console.log('Validating credentials:', {
                hasOAuth: !!metadata.oauth,
                hasCredentials: !!metadata.credentials,
                token: metadata.oauth?.accessToken ? `${metadata.oauth.accessToken.slice(0, 10)}...` : undefined
            });
            if (!this.config.oauth) {
                throw new Error('OAuth is not configured');
            }

            if (metadata.oauth?.accessToken) {
                const client = new SupabaseManagementAPI({accessToken: metadata.oauth?.accessToken});

                try {
                    const projects = await client.getProjects();
                    return true;
                } catch (error) {
                    if (isSupabaseError(error)) {
                        // error is now typed as SupabaseManagementAPI
                        console.log(
                            `Supabase Error:  ${error.message}, response status: ${error.response.status}`
                        );
                        return false;
                    }
                    return false;
                }
            } else if (metadata.credentials) {
                // Validate API key
                const {projectUrl, apiKey} = metadata.credentials;
                if (!projectUrl || !apiKey) {
                    console.error('Missing projectUrl or apiKey');
                    return false;
                }

                const supabase = createClient(projectUrl, apiKey);
                const {data, error} = await supabase.auth.getUser();

                if (error) {
                    console.error('Supabase client validation error:', error);
                    return false;
                }
                return !!data.user;
            }

            console.error('No credentials provided');
            return false;
        } catch (error) {
            const errorContext = {
                hasOAuth: !!metadata.oauth,
                hasCredentials: !!metadata.credentials,
                provider: this.provider,
                operation: 'validateCredentials',
                timestamp: new Date().toISOString()
            };
            console.error('Failed to validate Supabase credentials:', error, errorContext);
            return false;
        }
    }

    async callSupabaseAPI<T>(
        connectionId: string,
        apiCall: (token: string) => Promise<Response>
    ): Promise<T> {
        const connection = await this.getConnection({ connectionId });
        const { oauth } = connection.metadata as SupabaseMetadata;

        if (!oauth?.accessToken) {
            throw new SupabaseTokenError('No valid access token', { connectionId });
        }

        // Check if token needs refreshing before making the call
        if (oauth.refreshToken && this.shouldRefreshToken(oauth)) {
            console.log('Token is about to expire, proactively refreshing...');
            const newToken = await this.refreshToken(connectionId);
            const response = await apiCall(newToken.accessToken);

            if (!response.ok) {
                const errorContext = {
                connectionId,
                status: response.status,
                statusText: response.statusText,
                url: response.url,
                operation: 'callSupabaseAPI',
                timestamp: new Date().toISOString()
            };
            console.error('API call failed', errorContext);
            throw new SupabaseIntegrationError(`API call failed: ${response.statusText}`, errorContext);
            }

            return response.json();
        }

        // First attempt with current token
        const response = await apiCall(oauth.accessToken);

        // If token expired (401), try to refresh and retry
        if (response.status === 401 && oauth?.refreshToken) {
            console.log('Token expired, attempting to refresh...');

            // Refresh the token
            const newToken = await this.refreshToken(connectionId);

            // Retry with the new token
            const retryResponse = await apiCall(newToken.accessToken);

            if (!retryResponse.ok) {
                const errorContext = {
                connectionId,
                status: retryResponse.status,
                statusText: retryResponse.statusText,
                url: retryResponse.url,
                operation: 'callSupabaseAPI',
                tokenRefreshed: true,
                timestamp: new Date().toISOString()
            };
            console.error('API call failed after token refresh', errorContext);
            throw new SupabaseIntegrationError(`API call failed after token refresh: ${retryResponse.statusText}`, errorContext);
            }

            return retryResponse.json();
        }

        if (!response.ok) {
            const errorContext = {
                connectionId,
                status: response.status,
                statusText: response.statusText,
                url: response.url,
                operation: 'callSupabaseAPI',
                timestamp: new Date().toISOString()
            };
            console.error('API call failed', errorContext);
            throw new SupabaseIntegrationError(`API call failed: ${response.statusText}`, errorContext);
        }

        return response.json();
    }

    async callSupabaseSDK<T>(
        connectionId: string,
        sdkCall: (token: string) => Promise<T>
    ): Promise<T> {
        const connection = await this.getConnection({ connectionId });
        const { oauth } = connection.metadata as SupabaseMetadata;

        if (!oauth?.accessToken) {
            throw new SupabaseTokenError('No valid access token', { connectionId });
        }

        // Check if token needs refreshing before making the call
        if (oauth.refreshToken && this.shouldRefreshToken(oauth)) {
            console.log('Token is about to expire, proactively refreshing...');
            const newToken = await this.refreshToken(connectionId);
            return await sdkCall(newToken.accessToken);
        }

        try {
            // First attempt with current token
            return await sdkCall(oauth.accessToken);
        } catch (error) {
            // Check if it's a token expiration error (401 Unauthorized)
            if (isSupabaseError(error) &&
                error.response &&
                error.response.status === 401 &&
                oauth?.refreshToken) {

                console.log('Token expired, attempting to refresh...');

                // Refresh the token
                const newToken = await this.refreshToken(connectionId);

                try {
                    // Retry with the new token
                    return await sdkCall(newToken.accessToken);
                } catch (retryError) {
                    const errorContext = {
                    connectionId,
                    operation: 'callSupabaseSDK',
                    tokenRefreshed: true,
                    timestamp: new Date().toISOString()
                };
                console.error('API call failed after token refresh:', retryError, errorContext);
                    throw retryError;
                }
            }

            // If it's not a token error or we can't refresh, just throw the original error
            throw error;
        }
    }

    async refreshToken(connectionId: string): Promise<OAuthToken> {
        // If there's already a refresh in progress for this connection, reuse it
        const existingPromise = this.refreshTokenPromises[connectionId];
        if (existingPromise !== undefined) {
            console.log(`Reusing existing token refresh promise for connection ${connectionId}`);
            return existingPromise;
        }

        // Create a new refresh promise with timeout protection
        const refreshPromise = (async () => {
            try {
                // Get the connection with the expired token
                const connection: Connection = await this.getConnection({ connectionId });
                const { oauth } = connection.metadata as SupabaseMetadata;

                if (!oauth?.refreshToken) {
                    throw new SupabaseTokenError('No refresh token available', { connectionId });
                }

                if (!this.config.oauth) {
                    throw new SupabaseIntegrationError('OAuth is not configured for Supabase integration');
                }

                const { clientId, clientSecret, tokenUrl } = this.config.oauth;

                // Check if token is already expired or will expire soon (within 5 minutes)
                const shouldRefresh = this.shouldRefreshToken(oauth);
                if (!shouldRefresh) {
                    console.log('Token is still valid, no need to refresh');
                    return oauth;
                }

                console.log(`Refreshing token for connection ${connectionId}...`);
                // Prepare the refresh token request using form data for the body
                const formData = new URLSearchParams();
                formData.append('grant_type', 'refresh_token');
                formData.append('refresh_token', oauth.refreshToken);
                formData.append('client_id', clientId);
                formData.append('client_secret', clientSecret);

                const response = await fetch(tokenUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: formData
                });

                const responseText = await response.text();

                if (!response.ok) {
                    const errorContext = {
                        connectionId,
                        status: response.status,
                        statusText: response.statusText,
                        operation: 'refreshToken',
                        timestamp: new Date().toISOString()
                    };
                    console.error('Failed to refresh token', errorContext, { responseText });
                    throw new SupabaseTokenError(`Failed to refresh token: ${responseText}`, errorContext);
                }

                const data = JSON.parse(responseText);
                const newToken = {
                    accessToken: data.access_token,
                    refreshToken: data.refresh_token || oauth.refreshToken, // Use new refresh token if provided, otherwise keep the old one
                    expiresAt: new Date(Date.now() + data.expires_in * 1000).toISOString(),
                    tokenType: data.token_type,
                };

                // Update the connection with the new token
                await this.updateConnectionToken(connection, newToken);
                console.log(`Token refreshed successfully for connection ${connectionId}`);

                return newToken;
            } catch (error) {
                const errorContext = {
                    connectionId,
                    operation: 'refreshToken',
                    timestamp: new Date().toISOString()
                };
                console.error('Token refresh error:', error, errorContext);
                throw error;
            }
        })();

        // Add timeout protection (10 seconds)
        const timeoutPromise = new Promise<never>((_, reject) => {
            setTimeout(() => {
                reject(new SupabaseTokenError('Token refresh timeout after 10 seconds', { connectionId }));
            }, 10000);
        });

        // Store the promise with race protection for reuse
        const promiseWithTimeout = Promise.race([refreshPromise, timeoutPromise]);
        this.refreshTokenPromises[connectionId] = promiseWithTimeout;

        // Clean up the promise reference when done (success or error)
        promiseWithTimeout.finally(() => {
            delete this.refreshTokenPromises[connectionId];
        });

        return promiseWithTimeout;
    }

    async updateConnectionToken(connection: Connection, token: OAuthToken): Promise<void> {
        const metadata = { ...connection.metadata } as SupabaseMetadata;

        // Update the OAuth token in the metadata
        metadata.oauth = token;


        // Update the connection in the database
        await this.update({id: connection.id, metadata })
    }

    // async checkConnectionStatus() {
    //     // Get all Supabase connections
    //     const connections: any[] = await db
    //         .select()
    //         .from(connection)
    //         .where(
    //             and(
    //                 eq(connection.provider, 'supabase'),
    //                 eq(connection.status, 'connected')
    //             )
    //         );
    //
    //     const results: any = {
    //         working: [],
    //         broken: [],
    //         unknown: []
    //     };
    //
    //     const supabaseProvider = new SupabaseIntegrationProvider();
    //
    //     for (const conn of connections) {
    //         try {
    //             // Try to use the connection to make a simple API call
    //             await supabaseProvider.callSupabaseAPI(conn.id, async (token) => {
    //                 // Simple API call that should work with any valid token
    //                 return fetch('https://api.supabase.com/v1/projects', {
    //                     headers: { Authorization: `Bearer ${token}` }
    //                 });
    //             });
    //
    //             // If we get here, the connection is working
    //             results.working.push((conn as any).id);
    //         } catch (error: unknown) {
    //             if (error instanceof SupabaseTokenError &&
    //                 error.message.includes('No such refresh token')) {
    //                 // This is definitely a broken connection due to our bug
    //                 results.broken.push((conn as any).id);
    //             } else {
    //                 // Some other error - might be unrelated
    //                 results.unknown.push({id: conn.id, error: error.message});
    //             }
    //         }
    //     }
    //
    //     return results;
    // }

    async handleOAuthCallback(code: string): Promise<OAuthToken> {
        if (!this.config.oauth) {
            throw new SupabaseIntegrationError('OAuth is not configured for Supabase integration');
        }

        const {clientId, clientSecret, redirectUri, tokenUrl} = this.config.oauth;

        console.log('OAuth Config:', {
            tokenUrl,
            clientId,
            redirectUri,
            code
        });

        try {
            // Create form data parameters for the request body
            const formData = new URLSearchParams();
            formData.append('grant_type', 'authorization_code');
            formData.append('code', code);
            formData.append('client_id', clientId);
            formData.append('client_secret', clientSecret);
            formData.append('redirect_uri', redirectUri);
            // formData.append('scope', this.config.oauth.scope.join(',')); // Add scopes if needed

            // console.log('OAuth token exchange request:', {
            //     url: tokenUrl,
            //     method: 'POST',
            //     contentType: 'application/x-www-form-urlencoded',
            //     bodyParams: Object.fromEntries(formData.entries())
            // });

            const response = await fetch(tokenUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData
            });

            const responseText = await response.text();
            console.log('Token Response:', {
                status: response.status,
                headers: Object.fromEntries(response.headers.entries()),
                body: responseText
            });

            if (!response.ok) {
                throw new Error(`Failed to exchange code for token: ${responseText}`);
            }

            const data = JSON.parse(responseText);
            return {
                accessToken: data.access_token,
                refreshToken: data.refresh_token,
                expiresAt: new Date(Date.now() + data.expires_in * 1000).toISOString(),
                tokenType: data.token_type,
            };
        } catch (error) {
            console.error('Token exchange error:', error);
            throw error;
        }
    }

    async getLatestInstructionsForChat({project}: {project: Project}) {
        if (!project.connectionId || !project.supabaseProjectId) {
            throw new Error('Chat is not linked to a Supabase project');
        }

        console.log(`[SupabaseIntegration] Getting latest instructions for chat - Project ID: ${project.supabaseProjectId}`);

        const [schema, functions, secrets, rlsPolicies, dbFunctions, triggers, storageBuckets] = await Promise.all([
            this.callSupabaseSDK(project.connectionId, async (token) => {
                const client = new SupabaseManagementAPI({accessToken: token});

                // Get schema
                const schemaQuery = `
                    SELECT t.table_name,
                           json_agg(json_build_object(
                                   'column_name', c.column_name,
                                   'data_type', c.data_type,
                                   'is_nullable', c.is_nullable,
                                   'column_default', c.column_default
                                    )) as columns
                    FROM information_schema.tables t
                             JOIN information_schema.columns c ON t.table_name = c.table_name
                    WHERE t.table_schema = 'public'
                      AND t.table_type = 'BASE TABLE'
                    GROUP BY t.table_name
                    ORDER BY t.table_name;
                `;
                try {
                    return await client.runQuery(project.supabaseProjectId as string, schemaQuery);
                } catch (error) {
                    if (isSupabaseError(error)) {
                        // error is now typed as SupabaseManagementAPI
                        console.log(
                            `Supabase Error:  ${error.message}, response status: ${error.response.status}`
                        );
                    }
                    return [];
                }
            }),
            this.listFunctions({connectionId: project.connectionId, projectRef: project.supabaseProjectId}),
            this.getSecrets({connectionId: project.connectionId, projectRef: project.supabaseProjectId}),
            // Get RLS policies
            this.callSupabaseSDK(project.connectionId, async (token) => {
                const client = new SupabaseManagementAPI({accessToken: token});

                const rlsQuery = `
                    SELECT n.nspname as schema,
                           c.relname as table,
                           p.polname as policy_name,
                           p.polcmd as command,
                           p.polpermissive as permissive,
                           pg_get_expr(p.polqual, p.polrelid) as expression,
                           pg_get_expr(p.polwithcheck, p.polrelid) as with_check,
                           array_to_string(ARRAY(SELECT rolname FROM pg_roles WHERE oid = ANY(p.polroles)), ',') as roles
                    FROM pg_policy p
                    JOIN pg_class c ON p.polrelid = c.oid
                    JOIN pg_namespace n ON c.relnamespace = n.oid
                    WHERE n.nspname = 'public'
                    ORDER BY n.nspname, c.relname, p.polname;
                `;
                try {
                    return await client.runQuery(project.supabaseProjectId as string, rlsQuery);
                } catch (error) {
                    if (isSupabaseError(error)) {
                        console.log(
                            `Supabase Error fetching RLS policies: ${error.message}, response status: ${error.response.status}`
                        );
                    }
                    return [];
                }
            }),
            // Get database functions (not edge functions)
            this.callSupabaseSDK(project.connectionId, async (token) => {
                const client = new SupabaseManagementAPI({accessToken: token});

                const dbFunctionsQuery = `
                    SELECT n.nspname as schema,
                           p.proname as function_name,
                           pg_get_function_arguments(p.oid) as arguments,
                           t.typname as return_type,
                           p.prosrc as source_code,
                           p.provolatile as volatility
                    FROM pg_proc p
                    JOIN pg_namespace n ON p.pronamespace = n.oid
                    JOIN pg_type t ON p.prorettype = t.oid
                    WHERE n.nspname = 'public'
                    ORDER BY n.nspname, p.proname;
                `;
                try {
                    return await client.runQuery(project.supabaseProjectId as string, dbFunctionsQuery);
                } catch (error) {
                    if (isSupabaseError(error)) {
                        console.log(
                            `Supabase Error fetching DB functions: ${error.message}, response status: ${error.response.status}`
                        );
                    }
                    return [];
                }
            }),
            // Get triggers
            this.callSupabaseSDK(project.connectionId, async (token) => {
                const client = new SupabaseManagementAPI({accessToken: token});

                const triggersQuery = `
                    SELECT t.tgname as trigger_name,
                           c.relname as table_name,
                           pg_get_triggerdef(t.oid) as definition
                    FROM pg_trigger t
                    JOIN pg_class c ON t.tgrelid = c.oid
                    JOIN pg_namespace n ON c.relnamespace = n.oid
                    WHERE n.nspname = 'public' AND NOT t.tgisinternal
                    ORDER BY c.relname, t.tgname;
                `;
                try {
                    return await client.runQuery(project.supabaseProjectId as string, triggersQuery);
                } catch (error) {
                    if (isSupabaseError(error)) {
                        console.log(
                            `Supabase Error fetching triggers: ${error.message}, response status: ${error.response.status}`
                        );
                    }
                    return [];
                }
            }),
            // Get storage buckets
            this.callSupabaseSDK(project.connectionId, async (token) => {
                try {
                    // Use fetch directly since the SupabaseManagementAPI doesn't have a method for storage buckets
                    const response = await fetch(`https://api.supabase.com/v1/projects/${project.supabaseProjectId}/storage/buckets`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        console.log(
                            `Supabase Error fetching storage buckets: ${response.status} ${response.statusText}`
                        );
                        return [];
                    }

                    return await response.json();
                } catch (error) {
                    console.error('Error fetching storage buckets:', error);
                    return [];
                }
            })
        ]);

        // Format schema and related database objects in a more token-efficient way with progressive loading
        const formatSchemaForContext = (schema: any, rlsPolicies: any = [], dbFunctions: any = [], triggers: any = [], storageBuckets: any = []) => {
            // Create a compact JSON structure with essential information only
            const compactSchema: any = {
                tables: [],
                policies: [],
                triggers: [],
                functions: [],
                buckets: []
            };

            // Handle case where schema might not be an array
            const tables = Array.isArray(schema) ? schema : [];

            // Process tables with minimal information
            for (const table of tables) {
                const tableName = table.table_name;
                const tableInfo: any = {
                    name: tableName,
                    columns: []
                };

                // Add only essential column information
                if (Array.isArray(table.columns)) {
                    for (const column of table.columns) {
                        tableInfo.columns.push({
                            name: column.column_name || 'unknown',
                            type: column.data_type || 'unknown',
                            nullable: column.is_nullable === 'YES',
                            default: column.column_default || null
                        });
                    }
                }

                compactSchema.tables.push(tableInfo);
            }

            // Add minimal RLS policy information
            if (Array.isArray(rlsPolicies) && rlsPolicies.length > 0) {
                // Only include the first 10 policies to save space
                const limitedPolicies = rlsPolicies.slice(0, 10);
                for (const policy of limitedPolicies) {
                    compactSchema.policies.push({
                        name: policy.policy_name || 'unknown',
                        table: policy.table || '',
                        command: policy.command || '',
                        // Truncate expression to save space
                        expr: policy.expression ? policy.expression.substring(0, 100) : ''
                    });
                }

                // If there are more policies, add a note
                if (rlsPolicies.length > 10) {
                    compactSchema.policies.push({
                        name: "NOTE",
                        table: "system",
                        command: "info",
                        expr: `${rlsPolicies.length - 10} more policies available. Use progressive loading to see more.`
                    });
                }
            }

            // Add minimal trigger information
            if (Array.isArray(triggers) && triggers.length > 0) {
                // Only include the first 5 triggers to save space
                const limitedTriggers = triggers.slice(0, 5);
                for (const trigger of limitedTriggers) {
                    compactSchema.triggers.push({
                        name: trigger.trigger_name || 'unknown',
                        table: trigger.table_name || '',
                        // Truncate definition to save space
                        def: trigger.definition ? trigger.definition.substring(0, 100) : ''
                    });
                }

                // If there are more triggers, add a note
                if (triggers.length > 5) {
                    compactSchema.triggers.push({
                        name: "NOTE",
                        table: "system",
                        def: `${triggers.length - 5} more triggers available. Use progressive loading to see more.`
                    });
                }
            }

            // Add minimal DB function information - only names and signatures, no source code
            if (Array.isArray(dbFunctions) && dbFunctions.length > 0) {
                // Only include the first 5 functions to save space
                const limitedFunctions = dbFunctions.slice(0, 5);
                for (const func of limitedFunctions) {
                    compactSchema.functions.push({
                        name: func.function_name || 'unknown',
                        args: func.arguments || '',
                        ret: func.return_type || ''
                        // Completely omit source code to save space
                    });
                }

                // If there are more functions, add a note
                if (dbFunctions.length > 5) {
                    compactSchema.functions.push({
                        name: "NOTE",
                        args: "system",
                        ret: `${dbFunctions.length - 5} more functions available. Use progressive loading to see more.`
                    });
                }
            }

            // Add minimal storage bucket information
            if (Array.isArray(storageBuckets) && storageBuckets.length > 0) {
                for (const bucket of storageBuckets) {
                    compactSchema.buckets.push({
                        name: bucket.name || 'unknown',
                        public: bucket.public || false,
                        size_limit: bucket.file_size_limit || null
                        // Omit mime types to save space
                    });
                }
            }

            // Return JSON string of the compact schema
            return JSON.stringify(compactSchema);
        };

        // Create optimized schema format that includes RLS policies, triggers, storage buckets, etc.
        const optimizedSchema = formatSchemaForContext(schema, rlsPolicies, dbFunctions, triggers, storageBuckets);

        // Log storage buckets information
        if (storageBuckets && storageBuckets.length > 0) {
            console.log(`Found ${storageBuckets.length} storage buckets in Supabase project`);
            storageBuckets.forEach((bucket: any) => {
                console.log(`- Bucket: ${bucket.name} (Public: ${bucket.public ? 'Yes' : 'No'})`);
            });
        } else {
            console.log('No storage buckets found in Supabase project');
        }

        // Calculate size before optimization for comparison
        const originalSchemaSize = JSON.stringify(schema).length;
        const optimizedSchemaSize = optimizedSchema.length;
        const reductionPercentage = Math.round((1 - (optimizedSchemaSize / originalSchemaSize)) * 100);

        // Format edge functions in a more readable way
        const formatEdgeFunctions = (functions: any) => {
            if (!Array.isArray(functions) || functions.length === 0) return '';

            let result = '\nEDGE FUNCTIONS:\n';
            for (const func of functions) {
                // console.log('func', func)
                result += `- ${func.name || 'unnamed'}\n`;
                result += `- Function ID: ${func.ID || 'unnamed'}\n`;
                result += `  Status: ${func.status || 'unknown'}\n`;
                result += `  URL: ${func.slug ? `https://<project-ref>.supabase.co/functions/v1/${func.slug}` : 'N/A'}\n`;
                result += `  Created: ${func.created_at || 'unknown'}\n\n`;
            }
            return result;
        };

        const optimizedFunctions = formatEdgeFunctions(functions);
        const originalFunctionsSize = JSON.stringify(functions).length;
        const optimizedFunctionsSize = optimizedFunctions.length;

        // Calculate total optimization
        const totalOriginalSize = originalSchemaSize + originalFunctionsSize;
        const totalOptimizedSize = optimizedSchema.length + optimizedFunctionsSize;

        console.log(`[SupabaseIntegration] Schema optimization: ${originalSchemaSize} chars → ${optimizedSchemaSize} chars (${reductionPercentage}% reduction)`);
        console.log(`[SupabaseIntegration] Functions optimization: ${originalFunctionsSize} chars → ${optimizedFunctionsSize} chars`);
        console.log(`[SupabaseIntegration] Total optimization: ${totalOriginalSize} chars → ${totalOptimizedSize} chars (${Math.round((1 - (totalOptimizedSize / totalOriginalSize)) * 100)}% reduction)`);

        // Keep original code but use the optimized schema
        const instructions = await this.generateDataAndConnectInstructions({
            project: {
                id: project.supabaseProjectId, anonKey: project.supabaseAnonKey
            },
            types: null,
            // Use optimized schema instead of raw JSON
            schema: optimizedSchema,
            // Use optimized functions if available, otherwise use original
            functions: optimizedFunctions.length > 0 ? optimizedFunctions : functions,
            secrets,
            // Keep original data for reference but we've already included these in the optimized schema
            rlsPolicies,
            dbFunctions,
            triggers,
            storageBuckets
        });

        // Calculate and log sizes of each component
        const calculateSize = (obj: any) => {
            const json = JSON.stringify(obj);
            // Rough approximation of tokens (4 chars ≈ 1 token)
            const tokens = Math.ceil(json.length / 4);
            return {
                chars: json.length,
                tokens,
                kb: Math.round(json.length / 1024 * 100) / 100
            };
        };

        const schemaSize = calculateSize(schema);
        const functionsSize = calculateSize(functions);
        const secretsSize = calculateSize(secrets);
        const rlsPoliciesSize = calculateSize(rlsPolicies);
        const dbFunctionsSize = calculateSize(dbFunctions);
        const triggersSize = calculateSize(triggers);
        const storageBucketsSize = calculateSize(storageBuckets);
        const instructionsSize = calculateSize(instructions);
        const totalSize = calculateSize({
            instructions,
            schema,
            functions,
            secrets,
            rlsPolicies,
            dbFunctions,
            triggers,
            storageBuckets
        });

        console.log(`[SupabaseIntegration] Size metrics for project ${project.supabaseProjectId}:`);
        console.log(`Schema: ${schemaSize.chars} chars / ~${schemaSize.tokens} tokens / ${schemaSize.kb} KB`);
        console.log(`Functions: ${functionsSize.chars} chars / ~${functionsSize.tokens} tokens / ${functionsSize.kb} KB`);
        console.log(`Secrets: ${secretsSize.chars} chars / ~${secretsSize.tokens} tokens / ${secretsSize.kb} KB`);
        console.log(`RLS Policies: ${rlsPoliciesSize.chars} chars / ~${rlsPoliciesSize.tokens} tokens / ${rlsPoliciesSize.kb} KB`);
        console.log(`DB Functions: ${dbFunctionsSize.chars} chars / ~${dbFunctionsSize.tokens} tokens / ${dbFunctionsSize.kb} KB`);
        console.log(`Triggers: ${triggersSize.chars} chars / ~${triggersSize.tokens} tokens / ${triggersSize.kb} KB`);
        console.log(`Storage Buckets: ${storageBucketsSize.chars} chars / ~${storageBucketsSize.tokens} tokens / ${storageBucketsSize.kb} KB`);
        console.log(`Instructions: ${instructionsSize.chars} chars / ~${instructionsSize.tokens} tokens / ${instructionsSize.kb} KB`);
        console.log(`TOTAL: ${totalSize.chars} chars / ~${totalSize.tokens} tokens / ${totalSize.kb} KB`);

        return {
            instructions,
            schema,
            functions,
            secrets,
            rlsPolicies,
            dbFunctions,
            triggers,
            storageBuckets
        }
    }

    async getProjectData({projectRef, userId}: { projectRef: string; userId: string }) {
        const connection = await this.getConnectionByUserId({userId});
        if (!connection) {
            throw new SupabaseConnectionError('No Supabase connection found', { userId });
        }

        try {
            // Use callSupabaseSDK to handle token refresh automatically
            return await this.callSupabaseSDK(connection.id, async (accessToken) => {
                const client = new SupabaseManagementAPI({accessToken});
                // 1. Get databases
                const databases = await this.getDatabases({connectionId: connection.id});
                const project = databases.find(db => db.id === projectRef);
                if (!project) throw new Error('Project not found');

                const apiKeys = await client.getProjectApiKeys(projectRef);
                // 2. Get schema
                const schemaQuery = `
                    SELECT t.table_name,
                           json_agg(json_build_object(
                                   'column_name', c.column_name,
                                   'data_type', c.data_type,
                                   'is_nullable', c.is_nullable,
                                   'column_default', c.column_default
                                    )) as columns
                    FROM information_schema.tables t
                             JOIN information_schema.columns c ON t.table_name = c.table_name
                    WHERE t.table_schema = 'public'
                      AND t.table_type = 'BASE TABLE'
                    GROUP BY t.table_name
                    ORDER BY t.table_name;
                `;
                const [schema, functions, secrets] = await Promise.all([
                    client.runQuery(projectRef, schemaQuery),
                    client.listFunctions(projectRef),
                    client.getSecrets(projectRef)
                ]);

                // 3. Generate types without storing in fs
                // const {stdout: types} = await execAsync(
                //     `SUPABASE_ACCESS_TOKEN=${accessToken} npx supabase gen types typescript --project-id "${projectRef}"`
                // );
                project.anonKey = apiKeys?.find(key => key.name === "anon")?.api_key;

                // 4. Generate instructions for supabase
                const instructions = await this.generateDataAndConnectInstructions({
                    project,
                    types: null,
                    schema,
                    functions,
                    secrets
                });

                return {
                    project,
                    schema,
                    types: null,
                    instructions
                };
            });
        } catch (error) {
            if (isSupabaseError(error)) {
                const errorContext = {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    url: error.response.url,
                    operation: 'getProjectData',
                    projectRef,
                    userId
                };
                console.error(`Supabase API Error: ${error.message}`, errorContext);
            }
            throw error;
        }
    }

    async linkChatToProject({
        chatId,
        providerProjectId,
        connectionId,
        projectId
    }: {
        chatId: string;
        providerProjectId: string;
        connectionId: string;
        projectId: string
    }) {
        // Get the connection
        const [connectionDetails] = await db
            .select()
            .from(connection)
            .where(eq(connection.id, connectionId));

        if (!connectionDetails) {
            throw new Error('No Supabase connection found');
        }

        const metadata = connectionDetails.metadata;
        if (!metadata?.oauth?.accessToken) {
            throw new Error('No valid access token');
        }

        const client = new SupabaseManagementAPI({accessToken: metadata.oauth.accessToken});

        const apiKeys = await client.getProjectApiKeys(providerProjectId);

        // Simply update the chat with connection details
        await db
            .update(chat)
            .set({
                connectionId,
                supabaseProjectId: providerProjectId,
                supabaseAnonKey: apiKeys?.find(key => key.name === "anon")?.api_key,
                supabaseServiceKey: apiKeys?.find(key => key.name === "service_role")?.api_key,
            })
            .where(eq(chat.id, chatId));

        await db.update(projects)
            .set({
                connectionId,
                supabaseProjectId: providerProjectId,
                supabaseAnonKey: apiKeys?.find(key => key.name === "anon")?.api_key,
                supabaseServiceKey: apiKeys?.find(key => key.name === "service_role")?.api_key,
            })
            .where(eq(projects.id, projectId));
    }

    async getDatabases({connectionId}: { connectionId: string }): Promise<any[]> {
        return this.callSupabaseAPI<any[]>(connectionId, (token) => {
            return fetch('https://api.supabase.com/v1/projects', {
                headers: {
                    Authorization: `Bearer ${token}`
                },
            });
        });
    }

    /**
     * List all Edge Functions for a project
     * @param connectionId The connection ID
     * @param projectRef The Supabase project reference ID
     * @returns List of functions in the project
     */
    async listFunctions({connectionId, projectRef}: { connectionId: string; projectRef: string }) {
        return this.callSupabaseSDK(connectionId, async (accessToken) => {
            const client = new SupabaseManagementAPI({accessToken});
            return client.listFunctions(projectRef);
        });
    }

    /**
     * Get project resources of different types (functions, tables, storage buckets)
     * @param projectId The project ID in our system
     * @param resourceType The type of resource to fetch (functions, tables, storage)
     * @param userId The user ID making the request
     * @returns The requested resources for the project
     */
    /**
     * Get logs for a Supabase service
     * @param params Parameters for fetching logs
     * @returns The logs data
     */
    async getLogs({
        projectId,
        service,
        limit = 100,
        functionId
    }: {
        projectId: string;
        service: 'api' | 'branch-action' | 'postgres' | 'edge-function' | 'auth' | 'storage' | 'realtime';
        limit?: number;
        functionId?: string;
    }) {
        try {
            // Get the project from the database using direct query instead of schema
            const [project] = await db.select().from(projects).where(eq(projects.id, projectId)).limit(1);

            if (!project) {
                throw new SupabaseIntegrationError('Project not found', { projectId });
            }

            // Check if the project is connected to Supabase
            if (!project.supabaseProjectId || !project.connectionId) {
                throw new SupabaseIntegrationError('Project is not connected to Supabase', { projectId });
            }

            // Use the debugging tools to get the logs
            return await this.debuggingTools.getLogs({
                connectionId: project.connectionId,
                projectId: project.supabaseProjectId,
                service,
                limit,
                functionId,
                callSupabaseAPI: this.callSupabaseAPI.bind(this)
            });
        } catch (error) {
            throw new SupabaseIntegrationError('Failed to fetch Supabase logs', { 
                projectId,
                service,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    async getProjectResources({
        projectId,
        resourceType = 'functions'
    }: {
        projectId: string;
        resourceType?: 'functions' | 'tables' | 'storage';
    }) {
        try {
            // Get the project from the database using direct query instead of schema
            const [project] = await db.select().from(projects).where(eq(projects.id, projectId)).limit(1);

            if (!project) {
                throw new SupabaseIntegrationError('Project not found', { projectId });
            }

            // Check if the project is connected to Supabase
            if (!project.supabaseProjectId || !project.connectionId) {
                throw new SupabaseIntegrationError('Project is not connected to Supabase', { projectId });
            }

            // Handle different resource types
            switch (resourceType) {
                case 'functions':
                    const functions = await this.listFunctions({
                        connectionId: project.connectionId,
                        projectRef: project.supabaseProjectId,
                    });
                    return { functions };

                case 'tables':
                    // This would be implemented when adding table support
                    return { 
                        tables: [],
                        message: 'Table listing not yet implemented'
                    };

                case 'storage':
                    // This would be implemented when adding storage support
                    return { 
                        buckets: [],
                        message: 'Storage bucket listing not yet implemented'
                    };

                default:
                    throw new SupabaseIntegrationError('Invalid resource type', { resourceType });
            }
        } catch (error) {
            if (error instanceof SupabaseIntegrationError) {
                throw error;
            }
            
            console.error(`Error fetching Supabase resources:`, error);
            throw new SupabaseIntegrationError('Failed to fetch Supabase resources', { 
                projectId,
                resourceType,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    /**
     * Create a new Edge Function in a project
     * @param connectionId The connection ID
     * @param projectRef The Supabase project reference ID
     * @param functionData The function data to create
     * @returns The created function
     */
    async createFunction({connectionId, projectRef, functionData}: {
        connectionId: string;
        projectRef: string;
        functionData: CreateFunctionRequestBody
    }): Promise<CreateFunctionResponseData> {
        return this.callSupabaseSDK(connectionId, async (accessToken) => {
            const client = new SupabaseManagementAPI({accessToken});
            return client.createFunction(projectRef, functionData);
        });
    }

    /**
     * Update an existing Edge Function in a project
     * @param connectionId The connection ID
     * @param projectRef The Supabase project reference ID
     * @param slug The function slug to update
     * @param functionData The updated function data
     * @returns The updated function
     */
    async updateFunction({connectionId, projectRef, slug, functionData}: {
        connectionId: string;
        projectRef: string;
        slug: string;
        functionData: UpdateFunctionRequestBody
    }): Promise<UpdateFunctionResponseData> {
        return this.callSupabaseSDK(connectionId, async (accessToken) => {
            const client = new SupabaseManagementAPI({accessToken});
            return client.updateFunction(projectRef, slug, functionData);
        });
    }

    /**
     * Deploy a function using the new multipart/form-data API endpoint
     * @param connectionId The connection ID
     * @param projectRef The Supabase project reference ID
     * @param slug The function slug
     * @param files The function files
     * @param metadata The function metadata
     * @param bundleOnly Whether to just bundle the function without persisting it
     * @returns The deployed function data
     */
    async deployFunction({
        connectionId,
        projectRef,
        slug,
        files,
        metadata,
        bundleOnly = false
    }: {
        connectionId: string;
        projectRef: string;
        slug: string;
        files: Array<{name: string, content: string}>;
        metadata: {
            entrypoint_path: string;
            import_map_path?: string;
            static_patterns?: string[];
            verify_jwt?: boolean;
            name: string;
        };
        bundleOnly?: boolean;
    }): Promise<any> {
        return this.callSupabaseSDK(connectionId, async (accessToken) => {
            // Create FormData
            const formData = new FormData();

            // Add metadata
            formData.append('metadata', JSON.stringify(metadata));

            // Add files
            for (const file of files) {
                const blob = new Blob([file.content], { type: 'application/octet-stream' });
                formData.append('file', blob, file.name);
            }

            // Build URL with query parameters
            let url = `https://api.supabase.com/v1/projects/${projectRef}/functions/deploy`;
            const params = new URLSearchParams();
            if (slug) params.append('slug', slug);
            if (bundleOnly) params.append('bundleOnly', '1');
            if (params.toString()) url += `?${params.toString()}`;

            // Send request
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to deploy function: ${errorData.message || response.statusText}`);
            }

            return response.json();
        });
    }

    /**
     * Bulk update functions using the new API endpoint
     * @param connectionId The connection ID
     * @param projectRef The Supabase project reference ID
     * @param functions The functions to update
     * @returns The updated functions
     */
    async bulkUpdateFunctions({
        connectionId,
        projectRef,
        functions
    }: {
        connectionId: string;
        projectRef: string;
        functions: Array<{
            id?: string;
            slug: string;
            name: string;
            status?: string;
            version?: number;
            verify_jwt?: boolean;
            import_map?: boolean;
            entrypoint_path: string;
            import_map_path?: string;
        }>;
    }): Promise<any> {
        return this.callSupabaseSDK(connectionId, async (accessToken) => {
            const response = await fetch(`https://api.supabase.com/v1/projects/${projectRef}/functions`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(functions)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to bulk update functions: ${errorData.message || response.statusText}`);
            }

            return response.json();
        });
    }

    /**
     * Delete an Edge Function from a project
     * @param connectionId The connection ID
     * @param projectRef The Supabase project reference ID
     * @param slug The function slug to delete
     */
    async deleteFunction({connectionId, projectRef, slug}: {
        connectionId: string;
        projectRef: string;
        slug: string;
    }): Promise<void> {
        return this.callSupabaseSDK(connectionId, async (accessToken) => {
            const client = new SupabaseManagementAPI({accessToken});
            await client.deleteFunction(projectRef, slug);
        });
    }

    /**
     * Create secrets for a project
     * @param connectionId The connection ID
     * @param projectRef The Supabase project reference ID
     * @param secrets The secrets to create
     */
    async createSecrets({connectionId, projectRef, secrets}: {
        connectionId: string;
        projectRef: string;
        secrets: CreateSecretsRequestBody
    }): Promise<void> {
        return this.callSupabaseSDK(connectionId, async (accessToken) => {
            const client = new SupabaseManagementAPI({accessToken});
            await client.createSecrets(projectRef, secrets);
        });
    }

    /**
     * Delete secrets from a project
     * @param connectionId The connection ID
     * @param projectRef The Supabase project reference ID
     * @param secretNames The names of secrets to delete
     * @returns The response data
     */
    async deleteSecrets({connectionId, projectRef, secretNames}: {
        connectionId: string;
        projectRef: string;
        secretNames: DeleteSecretsRequestBody
    }): Promise<DeleteSecretsResponseData> {
        return this.callSupabaseSDK(connectionId, async (accessToken) => {
            const client = new SupabaseManagementAPI({accessToken});
            return client.deleteSecrets(projectRef, secretNames);
        });
    }

    /**
     * Get secrets for a project
     * @param connectionId The connection ID
     * @param projectRef The Supabase project reference ID
     * @returns The project secrets
     */
    async getSecrets({connectionId, projectRef}: {
        connectionId: string;
        projectRef: string;
    }) {
        return this.callSupabaseSDK(connectionId, async (accessToken) => {
            const client = new SupabaseManagementAPI({accessToken});
            return client.getSecrets(projectRef);
        });
    }

    async executeSQL({query, projectId}: {
        query: string;
        projectId: string
    }) {
        // Get project with connection details
        const [projectDetails] = await db
            .select({
                projects: projects,
                connection: connection
            })
            .from(projects)
            .innerJoin(
                connection,
                eq(projects.connectionId, connection.id)
            )
            .where(eq(projects.id, projectId));


        if (!projectDetails) {
            throw new SupabaseConnectionError('No connection found for this project', { projectId });
        }

        if (!projectDetails.projects.supabaseProjectId) {
            throw new SupabaseConnectionError('No Supabase project linked to this project', { projectId });
        }

        // Use callSupabaseSDK to handle token refresh automatically
        if (!projectDetails.connection.id) {
            throw new SupabaseConnectionError('Connection ID is missing', { projectId });
        }

        // Using non-null assertion since we've checked above
        const connectionId = projectDetails.connection.id as string;
        return this.callSupabaseSDK(connectionId, async (accessToken) => {
            const client = new SupabaseManagementAPI({accessToken});
            // Using non-null assertion since we've checked above
            const supabaseProjectId = projectDetails.projects.supabaseProjectId as string;
            return client.runQuery(supabaseProjectId, query);
        });
    }

    getSupabaseInitialInstructions() {
        return `
<SUPABASE_INSTRUCTIONS>

Please use getSupabaseLogs tool to fetch the latest logs from Supabase. 

<instructions>
        <instruction>Generate Supabase client and types from provided schema</instruction>
        <instruction>Return schema summary with field counts</instruction>
        <instruction>Suggest schema integrations</instruction>
        <instruction>Use only existing dependencies</instruction>
        <instruction>Analyze app screens for data structures</instruction>
        <instruction>Generate migrations for existing app data</instruction>
        <instruction>ALWAYS generate secrets for sensitive keys using MO_FILE tag</instruction>
        <instruction>ALWAYS prefer supabase edge functions for any data related tasks to avoid RLS</instruction>
        <instruction>ALWAYS make sure to integrate edge functions correctly using the correct urls and the methods</instruction>
        <instruction>Use JWT verified flag for apps that require authentication</instruction>
        <instruction>ALWAYS prefer adding supabase functions as soon as you can in the project</instruction>
        <instruction>Use Supabase Storage for file uploads and management when available</instruction>
        <instruction>Configure proper Storage bucket permissions and policies for secure file access</instruction>
        <instruction>Supabase edge functions are not visible to the user. Never assume something or put mock/dummy data in it without informing the user. ALWAYS keep the user in the loop and suggest best practices.</instruction>
    </instructions>

    <rules>
        <database>
            <rule>Wrap queries using editFile tool and by creating a .sql file with purpose comments</rule>
            <rule>Include up migrations for schema changes</rule>
            <rule>Analyze UI components for required data structures</rule>
            <rule>Generate migrations that preserve existing app functionality</rule>
            <rule>Put all the queries, migrations, RLS, triggers, database functions in the same query</rule>

            <example id="1">
               <explanation>
                  When a user connects their Supabase project:
                  1. Analyze the app's existing UI components and data structures
                  2. Look for patterns like lists, forms, or data displays that indicate required database tables
                  3. Generate appropriate migrations that match the app's current data needs
                  5. Ensure all existing mock data can be properly stored in the new schema
                  6. Return both schema creation and data migration queries if needed
                  7. DO NOT add any extra dependencies, supabase-js is already included
                  8. There is no need to add import 'react-native-url-polyfill/auto' as a dependency
                  9. The password reset flow consist of 2 broad steps: (i) Allow the user to login via the password reset link; (ii) Update the user's password. The resetPasswordForEmail() only sends a password reset link to the user's email. To update the user's password, see updateUser(). A PASSWORD_RECOVERY event will be emitted when the password recovery link is clicked. You can use onAuthStateChange() to listen and invoke a callback function on these events. When the user clicks the reset link in the email they are redirected back to your application. You can configure the URL that the user is redirected to with the redirectTo parameter. See redirect URLs and wildcards to add additional redirect URLs to your project. After the user has been redirected successfully, prompt them for a new password and call updateUser():
                 10. As we are working in expo snack, the url is not the correct url. But we will get the onAuthStateChange, why don't you subscribe to it and then show the reset password form? Do you understand?
                 11. ALWAYS show the correct states from Supabase with errors, warnings, states so that the USER DOES NOT get stuck.
                 12. Form validations, error states, errors feedback, response feedback (Reset password sent, Email confirmation needed, invalid password, already registered and other states from Supabase)...
                </explanation>
                <user>Please connect my supabase project yrsdqwemtqgdwoixrrge with this app</user>
                <assistant_response>
                    Analyzing app screens and data structures...

                    editFile({
                        ...
                         -- Purpose: Create initial schema based on app data
                        CREATE TABLE IF NOT EXISTS users (
                            id UUID DEFAULT auth.uid() PRIMARY KEY,
                            email TEXT UNIQUE,
                            name TEXT,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        );

                        -- Migrate existing mock data structure
                        CREATE TABLE IF NOT EXISTS tasks (
                            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                            user_id UUID REFERENCES users(id),
                            title TEXT NOT NULL,
                            description TEXT,
                            status TEXT DEFAULT 'pending',
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        );
                    })
                </assistant_response>
            </example>

         
        </database>

        <security>
            <rule>Validate and sanitize data</rule>
        </security>

        <storage>
            <rule>Use createClient().storage to access Storage buckets</rule>
            <rule>Set appropriate MIME type restrictions for file uploads</rule>
            <rule>Implement file size limits to prevent abuse</rule>
            <rule>Use signed URLs for secure file access when needed</rule>
            <rule>Create appropriate RLS policies for Storage buckets</rule>
        </storage>
    </rules>

    <urls_for_convinience>
        <instructions>
            <instruction>1. If the user needs to perform an administrative task on the supabase dashboard, always return the link to their dashboard with the correct url for quick navigation to the page</instruction>
            <examples>
                <example>
                    If the user needs to configure google auth on their supabase dashboard, respond with a link ALWAYS at the bottom of the message in the format https://supabase.com/dashboard/project/<projectID>/auth/providers
                    Always mention what values they need to enter in which fields on the page
                </example>
            </examples>
        </instructions>
    </urls_for_convinience>
</SUPABASE_INSTRUCTIONS>
`
    }

    async generateDataAndConnectInstructions({project, types, schema, functions, secrets, rlsPolicies, dbFunctions, triggers, storageBuckets}: {
        project: { id: string, anonKey: string | null },
        types: string | null,
        schema: any, // Can now be either JSON array or formatted string
        functions: any,
        secrets: any,
        rlsPolicies?: any,
        dbFunctions?: any,
        triggers?: any,
        storageBuckets?: any
    }) {

        const sanitizeSecrets = (secrets || []).map((secret: any) => {
            secret.value = `${secret.value} ? <READACTED_BUT_SET>: <REDACTED_BUT_NOT_SET>`;
            return secret
        })

        return `<extended_system_prompt>

   ${this.getSupabaseInitialInstructions()}


    <supabase_attached_data>
        ${project ? `<supabase-project>${JSON.stringify(project, null, 2)}</supabase-project>` : undefined}
        ${types ? `<auto_generated_types>${JSON.stringify(types, null, 2)}</auto_generated_types>` : undefined}
        <latest_database_schemas>
        ${typeof schema === 'string' ? schema : JSON.stringify(schema, null, 2)}
        </latest_database_schemas>
        ${typeof functions === 'string' ? functions : (functions ? `<supabase_functions>${JSON.stringify(functions, null, 2)}</supabase_functions>` : undefined)}
        ${functions ? `<supabase_secrets>${JSON.stringify(sanitizeSecrets, null, 2)}</supabase_secrets>` : undefined}
        ${storageBuckets && storageBuckets.length > 0 ? `<supabase_storage_buckets>${JSON.stringify(storageBuckets, null, 2)}</supabase_storage_buckets>` : undefined}
        <!-- RLS policies, DB functions, triggers, and storage buckets are now included in the optimized schema format above -->
    </supabase_attached_data>
</extended_system_prompt>`
    }

    /**
     * Get auth configuration for a Supabase project
     * @param projectId The project ID in our system
     * @returns The auth configuration data
     */
    async getAuthConfig(projectId: string) {
        try {
            // Get the project from the database
            const [project] = await db.select().from(projects).where(eq(projects.id, projectId)).limit(1);

            if (!project) {
                throw new SupabaseIntegrationError('Project not found', { projectId });
            }

            // Check if the project is connected to Supabase
            if (!project.supabaseProjectId || !project.connectionId) {
                throw new SupabaseIntegrationError('Project is not connected to Supabase', { projectId });
            }

            return this.callSupabaseSDK(project.connectionId, async (accessToken) => {
                const client = new SupabaseManagementAPI({accessToken});
                const supabaseProjectId = project.supabaseProjectId as string;
                return client.getProjectAuthConfig(supabaseProjectId)
            });
        } catch (error) {
            throw new SupabaseIntegrationError('Failed to fetch Supabase auth config', { 
                projectId,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    /**
     * Update auth configuration for a Supabase project
     * @param projectId The project ID in our system
     * @param authConfig The auth config object to update
     * @returns The updated auth configuration data
     */
    async updateAuthConfig(projectId: string, authConfig: UpdateProjectAuthConfigRequestBody) {
        try {
            // Get the project from the database
            const [project] = await db.select().from(projects).where(eq(projects.id, projectId)).limit(1);

            if (!project) {
                throw new SupabaseIntegrationError('Project not found', { projectId });
            }

            // Check if the project is connected to Supabase
            if (!project.supabaseProjectId || !project.connectionId) {
                throw new SupabaseIntegrationError('Project is not connected to Supabase', { projectId });
            }

            return this.callSupabaseSDK(project.connectionId, async (accessToken) => {
                const client = new SupabaseManagementAPI({accessToken});
                const supabaseProjectId = project.supabaseProjectId as string;
                return client.updateProjectAuthConfig(supabaseProjectId, authConfig)
            });
        } catch (error) {
            throw new SupabaseIntegrationError('Failed to update Supabase auth config', { 
                projectId,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    /**
     * Get SSO providers for a Supabase project
     * @param projectId The project ID in our system
     * @returns The SSO providers data
     */
    async getSSOProviders(projectId: string) {
        try {
            // Get the project from the database
            const [project] = await db.select().from(projects).where(eq(projects.id, projectId)).limit(1);

            if (!project) {
                throw new SupabaseIntegrationError('Project not found', { projectId });
            }

            // Check if the project is connected to Supabase
            if (!project.supabaseProjectId || !project.connectionId) {
                throw new SupabaseIntegrationError('Project is not connected to Supabase', { projectId });
            }

            return this.callSupabaseSDK(project.connectionId, async (accessToken) => {
                const client = new SupabaseManagementAPI({accessToken});
                const supabaseProjectId = project.supabaseProjectId as string;
                return client.getSSOProviders(supabaseProjectId)
            });
        } catch (error) {
            throw new SupabaseIntegrationError('Failed to fetch Supabase SSO providers', { 
                projectId,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }
}
