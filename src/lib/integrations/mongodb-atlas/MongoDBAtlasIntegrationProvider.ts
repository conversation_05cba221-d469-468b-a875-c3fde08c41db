import { BaseIntegrationProvider } from '../base/BaseIntegrationProvider';
import { IntegrationConfig, IntegrationMetadata } from '../base/types';
import { db } from '@/lib/db/db';
import { projects, oauthClients } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { nanoid } from 'nanoid';

export interface MongoDBAtlasApp {
  id: string;
  name: string;
  appId: string;
  clusterName: string;
  connectionString: string;
  apiKey: string;
}

export class MongoDBAtlasIntegrationProvider extends BaseIntegrationProvider {
  constructor() {
    const config: IntegrationConfig = {
      apiKey: {
        required: true,
        fields: [
          {
            key: 'publicKey',
            label: 'MongoDB Atlas Public Key',
            type: 'text',
            required: true,
          },
          {
            key: 'privateKey',
            label: 'MongoDB Atlas Private Key',
            type: 'password',
            required: true,
          },
          {
            key: 'projectId',
            label: 'Atlas Project ID',
            type: 'text',
            required: true,
          },
        ],
      },
    };
    super('mongodb-atlas', config);
  }

  async validateCredentials(metadata: IntegrationMetadata): Promise<boolean> {
    try {
      if (!metadata.credentials?.publicKey || !metadata.credentials?.privateKey) {
        return false;
      }

      // Test API credentials by listing projects
      const response = await this.makeAtlasRequest(
        'GET',
        '/groups',
        metadata.credentials
      );

      return response.ok;
    } catch (error) {
      console.error('Failed to validate MongoDB Atlas credentials:', error);
      return false;
    }
  }

  /**
   * Create a new MongoDB Atlas App Services project for a user's app
   */
  async createProject({
    userId,
    appName,
    projectId,
  }: {
    userId: string;
    appName: string;
    projectId: string;
  }): Promise<MongoDBAtlasApp> {
    try {
      // Get user's Atlas connection (admin setup)
      const connection = await this.getConnectionByUserId(userId);
      if (!connection?.metadata?.credentials?.publicKey) {
        throw new Error('No MongoDB Atlas connection found for user');
      }

      const { publicKey, privateKey, projectId: atlasProjectId } = connection.metadata.credentials;
      const appServiceName = `${appName.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${nanoid(8)}`;

      console.log('privateKey', privateKey, publicKey)
      // Create Atlas App Services application
      const appResponse = await this.makeAtlasRequest(
        'POST',
        `/groups/${atlasProjectId}/apps`,
        { publicKey, privateKey },
        {
          name: appServiceName,
          deployment_model: 'GLOBAL',
          environment: 'development',
        }
      );

      if (!appResponse.ok) {
        const error = await appResponse.text();
        throw new Error(`Failed to create Atlas App Services: ${error}`);
      }

      const atlasApp = await appResponse.json();

      // Create OAuth client for this app
      const clientId = nanoid(32);
      const clientSecret = nanoid(64);

      await db.insert(oauthClients).values({
        clientId,
        clientSecret,
        appId: projectId,
        redirectUri: `${process.env.NEXT_PUBLIC_APP_URL}/projects/${projectId}/callback/magically`,
        name: appName,
      });

      // Update project with Atlas details
      await db.update(projects)
        .set({
          mongodbAtlasAppId: atlasApp._id,
          mongodbAtlasUrl: atlasApp.domain || `https://realm.mongodb.com/groups/${atlasProjectId}/apps/${atlasApp._id}`,
          mongodbAtlasProjectId: atlasProjectId,
        })
        .where(eq(projects.id, projectId));

      return {
        id: atlasApp._id,
        name: appServiceName,
        appId: atlasApp._id,
        clusterName: atlasApp.cluster_name || 'default',
        connectionString: atlasApp.connection_string || '',
        apiKey: atlasApp.api_key || '',
      };
    } catch (error) {
      console.error('Failed to create MongoDB Atlas project:', error);
      throw error;
    }
  }

  /**
   * Deploy schema and functions to Atlas App Services
   */
  async deployToProject({
    projectId,
    schema,
    functions,
  }: {
    projectId: string;
    schema: Record<string, any>;
    functions: Record<string, string>;
  }): Promise<void> {
    try {
      // Get project details
      const [project] = await db
        .select()
        .from(projects)
        .where(eq(projects.id, projectId))
        .limit(1);

      if (!project) {
        throw new Error('Project not found');
      }

      // TODO: Implement Atlas App Services deployment
      // This would involve:
      // 1. Creating Realm schemas
      // 2. Creating Atlas Functions
      // 3. Setting up Device Sync rules
      // 4. Configuring authentication
      
      console.log('Deploying to Atlas App Services:', {
        projectId,
        atlasAppId: project.mongodbAtlasAppId,
        schema: Object.keys(schema),
        functions: Object.keys(functions),
      });
    } catch (error) {
      console.error('Failed to deploy to Atlas App Services:', error);
      throw error;
    }
  }

  /**
   * Get OAuth configuration for an app
   */
  async getOAuthConfig(appId: string): Promise<{
    clientId: string;
    authUrl: string;
    tokenUrl: string;
  }> {
    try {
      const [client] = await db
        .select()
        .from(oauthClients)
        .where(eq(oauthClients.appId, appId))
        .limit(1);

      if (!client) {
        throw new Error('OAuth client not found for app');
      }

      const baseUrl = process.env.NEXT_PUBLIC_APP_URL;

      return {
        clientId: client.clientId,
        authUrl: `${baseUrl}/api/oauth/authorize`,
        tokenUrl: `${baseUrl}/api/oauth/token`,
      };
    } catch (error) {
      console.error('Failed to get OAuth config:', error);
      throw error;
    }
  }

  /**
   * Make authenticated request to MongoDB Atlas API
   */
  private async makeAtlasRequest(
    method: string,
    endpoint: string,
    credentials: { publicKey: string; privateKey: string },
    body?: any
  ): Promise<Response> {
    const url = `https://cloud.mongodb.com/api/atlas/v1.0${endpoint}`;
    
    // Create digest auth header
    const auth = Buffer.from(`${credentials.publicKey}:${credentials.privateKey}`).toString('base64');
    
    const options: RequestInit = {
      method,
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    };

    if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(body);
    }

    return fetch(url, options);
  }

  private async getConnectionByUserId(userId: string) {
    // This would get the admin's Atlas connection
    // For now, we'll assume there's a system-wide Atlas connection
    // In production, you'd have admin credentials configured
    return {
      metadata: {
        credentials: {
          publicKey: process.env.MONGODB_ATLAS_PUBLIC_KEY,
          privateKey: process.env.MONGODB_ATLAS_PRIVATE_KEY,
          projectId: process.env.MONGODB_ATLAS_PROJECT_ID,
        },
      },
    };
  }
}
