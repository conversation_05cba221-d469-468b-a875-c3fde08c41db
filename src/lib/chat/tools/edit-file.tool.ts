import {fileEditSchema} from "@/lib/chat/schemas/file-edit.schema";
import {DataStreamWriter, tool} from "ai";
import {FileLineManager} from "@/lib/editor/FileLineManager";
import {FileContentManager} from "@/lib/editor/FileContentManager";
import {DiffApplicationService} from "@/lib/editor/DiffApplicationService";
import {TargetedValidator, ValidationResult} from "@/lib/services/targeted-validator";
import {DiffMeta} from "@/lib/parser/DiffParser";
import * as fs from 'fs';
import * as path from 'path';
import {SupabaseIntegrationProvider} from '@/lib/integrations/supabase/SupabaseIntegrationProvider';


// We used to block SQL files, but now we're allowing them and will run migrations automatically
const VALIDATORS: { name: string, check: (absolutePath: string, currentContents: string) => boolean, error: string }[] = [
    // SQL files are now allowed and will be processed automatically
    {
        name: 'REQUIREMENTS.md blocked',
        check: (absolutePath, currentContents) => {
            return absolutePath.includes("REQUIREMENTS.md") && (!currentContents?.includes("<ALLOW_EDIT>"))
        },
        error: "REQUIREMENTS.md cannot be edited to ensure the requirements are not changed. If you absolutely have to edit the file, please add <ALLOW_EDIT> at the top of the file and once edited, remove it. REMEMBER, contiously editing this file means the goal post keeps moving. Only update only if the requirements have truly changed. This file should contain the overall requirements throughout the app and not a part of it."
    }
]

export const editFileTool = ({
                                 fileManager,
                                 contentManager,
                                 processImagePlaceholders,
                                 processVideoPlaceholders,
                                 dataStream,
                                 creditUsageTracker,
                                 projectId
                             }: {
    fileManager: FileLineManager,
    contentManager: FileContentManager,
    dataStream: DataStreamWriter,
    processImagePlaceholders: (text: string) => Promise<string>,
    processVideoPlaceholders: (text: string) => Promise<string>,
    creditUsageTracker?: any,
    projectId: string
}) => {
    return tool({
        description: 'Edit an existing file using search and replace operations, similar to MO_DIFF. This eliminates line shift issues and is more reliable. IMPORTANT: When making multiple edits to the same file, batch them together in a single call to avoid conflicts. Always validate your edits with the TargetedValidator after making changes.',
        parameters: fileEditSchema,
        execute: async ({
                            absolutePath,
                            edits
                        }: any) => {
            try {
                console.log('Editing file:', absolutePath);
                console.log('Number of edits:', edits.length);

                // Add checks to detect if the llm is giving a sql file or a invalid file not supported,
                // we will return immediately without applying to course correct
                const errors: string[] = VALIDATORS.reduce((acc, validator) => {
                    if (validator.check(absolutePath, fileManager.hasFile(absolutePath) ? fileManager.getFinalContent(absolutePath): '')) {
                        acc.push(validator.error);
                    }
                    return acc;
                }, [] as string[]);

                if (errors.length) {
                    console.log('Errors found. Rejecting file edit', errors.join(','))
                    return errors.join(',')
                }

                // Check if file exists in the file manager
                let fileExists = true;
                let currentContent: string | null = "";
                try {
                    currentContent = fileManager.getFinalContent(absolutePath);
                } catch (e) {
                    fileExists = false;
                }

                // Prevent editing existing SQL files - SQL migrations should never be edited
                const isSqlMigration = absolutePath.toLowerCase().endsWith('.sql');
                if (isSqlMigration && fileExists) {
                    return `Cannot edit existing SQL migration ${absolutePath}. Please create a new migration file instead.`;
                }

                // If file doesn't exist, create it with empty content
                if (!fileExists) {
                    fileManager.addFiles(absolutePath, "");
                    contentManager.setFileContent(absolutePath, "");
                    console.log(`Created new file ${absolutePath} as it didn't exist`);
                } else {
                    // Ensure the content manager has the current content
                    contentManager.setFileContent(absolutePath, currentContent);
                }

                // Create a diff meta object to use with the DiffApplicationService
                const diffMeta: DiffMeta = {
                    path: absolutePath,
                    lang: path.extname(absolutePath).substring(1) || 'text',
                    searches: [],
                    replacements: [],
                    currentPair: 0,
                    lineCount: 0
                };

                // Process each edit as a search/replace pair
                for (const edit of edits) {
                    // Get the current content again as it might have changed
                    currentContent = contentManager.getFileContent(absolutePath);

                    // For new files with no content, handle differently
                    if (!currentContent && edit.searchPattern === '') {
                        diffMeta.searches.push('');
                        diffMeta.replacements.push(edit.replacementContent);
                        continue;
                    }

                    // Add the search/replace pair to the diff meta
                    diffMeta.searches.push(edit.searchPattern);

                    // If isAppend is true, we're inserting before the search pattern
                    if (edit.isAppend) {
                        diffMeta.replacements.push(edit.replacementContent + edit.searchPattern);
                    } else {
                        diffMeta.replacements.push(edit.replacementContent);
                    }

                    // Log the edit for debugging
                    console.log(`Edit ${diffMeta.searches.length}: ${edit.description || 'No description'}`);
                }

                // Apply the diff using the content manager
                console.log(`Applying ${diffMeta.searches.length} search/replace pairs to ${absolutePath}`);
                const result = await contentManager.applyDiff(diffMeta, {bestEffort: true, filePath: absolutePath});

                if (!result.success) {
                    return `Error applying changes to ${absolutePath}: ${result.message}`;
                }

                // Get the updated content
                let updatedContent: string | null = contentManager.getFileContent(absolutePath);

                if (!updatedContent) {
                    return `Error applying changes to ${absolutePath}: Something wrong with the replacement. Please ask the user <NAME_EMAIL> to get it fixed immediately.`;
                }

                // Update the file manager to keep it in sync
                fileManager.replaceFile(absolutePath, updatedContent);

                // Process placeholders
                updatedContent = await processImagePlaceholders(updatedContent);
                updatedContent = await processVideoPlaceholders(updatedContent);

                // Update content after processing placeholders
                contentManager.setFileContent(absolutePath, updatedContent);
                fileManager.replaceFile(absolutePath, updatedContent);

                // Validate the file for syntax errors
                const fileName = path.basename(absolutePath);
                const validationResult = TargetedValidator.validateFiles([{
                    name: fileName,
                    content: updatedContent
                }]);

                // Send validation results to the client
                dataStream.writeData({
                    type: 'validation-result',
                    content: {
                        path: absolutePath,
                        isValid: validationResult.isValid,
                        summary: validationResult.summary
                    }
                });

                // Track credit usage if available
                if (creditUsageTracker) {
                    creditUsageTracker.trackOperation('file_change');
                }

                // Check if this is a SQL file and run the migration if it is
                const isSqlFile = absolutePath.toLowerCase().endsWith('.sql');
                let sqlResult = '';
                let migrationStatus: 'success' | 'failed' = 'failed';
                
                // Save original content for potential rollback
                const originalContent = fileExists ? fileManager.getFinalContent(absolutePath) : '';
                
                if (isSqlFile) {
                    try {
                        // Use the project ID passed to the tool
                        if (projectId) {
                            // Execute the SQL migration
                            const supabase = new SupabaseIntegrationProvider();
                            const result = await supabase.executeSQL({
                                projectId,
                                query: updatedContent,
                            });

                            sqlResult = `SQL migration executed successfully for ${absolutePath}. Result: ${JSON.stringify(result)}`;
                            migrationStatus = 'success';
                            console.log(sqlResult);
                        } else {
                            sqlResult = `Could not execute SQL migration: No project ID provided`;
                            console.warn(sqlResult);
                            migrationStatus = 'failed';
                            
                            // Rollback the file if migration failed due to missing project ID
                            if (fileExists) {
                                fileManager.rollbackFile(absolutePath, originalContent);
                                contentManager.setFileContent(absolutePath, originalContent);
                            } else {
                                // For new files, just remove them
                                fileManager.rollbackFile(absolutePath);
                                contentManager.setFileContent(absolutePath, '');
                            }
                            
                            return `SQL migration failed: ${sqlResult}. Changes to ${absolutePath} were not applied and the file has been rolled back.`;
                        }
                    } catch (error: any) {
                        sqlResult = `Error executing SQL migration: ${error.message}`;
                        migrationStatus = 'failed';
                        console.error(sqlResult);
                        
                        // Rollback the file if migration failed due to an error
                        if (fileExists) {
                            fileManager.rollbackFile(absolutePath, originalContent);
                            contentManager.setFileContent(absolutePath, originalContent);
                        } else {
                            // For new files, just remove them
                            fileManager.rollbackFile(absolutePath);
                            contentManager.setFileContent(absolutePath, '');
                        }
                        
                        return `SQL migration failed: ${sqlResult}. Changes to ${absolutePath} were not applied and the file has been rolled back.`;
                    }
                }

                // Send the file operation to the client
                dataStream.writeData({
                    type: 'file-operation',
                    content: {
                        type: fileExists ? 'edit' : 'create',
                        absolutePath,
                        content: updatedContent
                    }
                });

                if (!validationResult.isValid) {
                    return `Updated file ${absolutePath}, but found validation issues: ${validationResult.summary}`;
                }

                // Add SQL-specific success message
                if (absolutePath.toLowerCase().endsWith('.sql')) {
                    if (migrationStatus === 'failed') {
                        return `File ${fileExists ? 'Updated' : 'Created'} SQL file ${absolutePath} successfully. Migration failed: ${sqlResult}`;
                    }
                    return `${fileExists ? 'Updated' : 'Created'} SQL file ${absolutePath} successfully and executed the migration. Migration ran successfully.`;
                }

                return `${fileExists ? 'Updated' : 'Created'} file ${absolutePath} successfully using search and replace!`;
            } catch (e: any) {
                console.log('Error while applying edits', e);
                return `Error while applying changes. Please rethink and make the changes again. Error: ${e.message}`
            }
        }
    })
}