import {z} from 'zod';
import {ContextEngine, CodeSnippet} from '@/lib/services/context-engine';
import {getProjectById} from '@/lib/db/project-queries';
import {tool} from "ai";
import {Project} from "@/lib/db/schema";
import {FileItem} from "@/types/file";
import {ToolCountTracker} from './tool-count-tracker';
import {FileLineManager} from "@/lib/editor/FileLineManager";

/**
 * Format code snippets as a readable string grouped by file
 */
function formatSnippetsAsString(snippets: CodeSnippet[]): string {
    if (!snippets || snippets.length === 0) {
        return "No relevant code snippets found.";
    }

    // Group snippets by file
    const snippetsByFile = new Map<string, CodeSnippet[]>();

    for (const snippet of snippets) {
        if (!snippetsByFile.has(snippet.filePath)) {
            snippetsByFile.set(snippet.filePath, []);
        }
        snippetsByFile.get(snippet.filePath)!.push(snippet);
    }

    // Sort files by relevance (highest scoring snippet first)
    const sortedFiles = Array.from(snippetsByFile.entries()).sort((a, b) => {
        const maxScoreA = Math.max(...a[1].map(s => s.score || 0));
        const maxScoreB = Math.max(...b[1].map(s => s.score || 0));
        return maxScoreB - maxScoreA;
    });

    const formattedSections: string[] = [];

    for (const [filePath, fileSnippets] of sortedFiles) {
        // Sort snippets within file by line number
        const sortedSnippets = fileSnippets.sort((a, b) => a.startLine - b.startLine);

        for (const snippet of sortedSnippets) {
            const lineRange = snippet.startLine === snippet.endLine
                ? `Line ${snippet.startLine}`
                : `Lines ${snippet.startLine}-${snippet.endLine}`;

            const symbolsText = snippet.symbols.length > 0
                ? snippet.symbols.join(', ')
                : 'N/A';

            const section = [
                `File: ${filePath} (${lineRange})`,
                `Type: ${snippet.type}`,
                `Symbols: ${symbolsText}`,
                '',
                snippet.content,
                '',
                '---',
                ''
            ].join('\n');

            formattedSections.push(section);
        }
    }

    return formattedSections.join('\n');
}

/**
 * Schema for the query codebase tool
 */
export const QueryCodebaseSchema = z.object({
    query: z.string().describe('The natural language query about the codebase. You can request either breadth first to get a holistic picture or specific lookups. PLAN in a way that minimizes context window usage.'),
    excludedFiles: z.array(z.string()).optional().describe('Array of file paths to exclude from the analysis. Include files that you have already requested earlier and are already sure of the contents about.'),
    reason: z.string().describe('ANSWER all questions: Explain the current state of your mind and why you need this file. Could you have done without it or is absolutely necessary to get this tool call? Is the cost justified to make this call?'),
});

/**
 * Tool for querying the codebase using the context engine
 * This replaces the need for multiple getFileContents calls
 */
export const queryCodebase = ({
                                  projectId,
                                  files,
                                  fileManager,
                                  messageId = '',
                                  userMessage = ''
                              }: { projectId: string, files: FileItem[], fileManager: FileLineManager, messageId?: string, userMessage?: string }) => {
    return tool({
        description: 'Query the codebase to understand its structure, relationships, and relevant files for a specific task. ' +
            'Use this instead of requesting individual files. ' +
            'You can exclude specific files from the analysis by providing an array of file paths in the excludedFiles parameter. ' +
            'Use filePath parameter to retrieve a SINGLE file in full ONLY when absolutely necessary. You are LIMITED to ONE file per request.',
        parameters: QueryCodebaseSchema,
        execute: async ({query, excludedFiles, reason}: z.infer<typeof QueryCodebaseSchema>) => {
            try {
                console.log('Query:', query)
                console.log('Reason:', reason)
                console.log('Excluded files:', excludedFiles?.join())

                if(!query) {
                    return {
                        result: null,
                        message: "One of query or filePath is required. To look at the holistic view, use query and to look at a specific file, use filePath."
                    }
                }
                // Check if this is a "continue" message
                const isContinueMessage = userMessage.toLowerCase().trim().startsWith('continue');

                // Get the tool tracker instance
                const toolTracker = ToolCountTracker.getInstance();

                console.log('Tool call allowed (queryCodebase):', toolTracker.shouldAllowToolCall(messageId, 'queryCodebase'))

                // Check if we should allow this tool call
                if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'queryCodebase')) {
                    return {
                        result: null,
                        message: "⚠️ Tool call limit reached. You've already made multiple codebase queries. Please implement or summarize with the information you already have.",
                    };
                }

                // Increment the tool count if we have a chat ID
                if (messageId) {
                    toolTracker.incrementToolCount(messageId, 'queryCodebase');
                }
                if (!files || files.length === 0) {
                    throw new Error('No files available for context engine');
                }

                // Get project information if available
                let project: Project | null = null;
                if (projectId) {
                    try {
                        project = await getProjectById({id: projectId});
                    } catch (error) {
                        console.warn('Could not fetch project information:', error);
                        // Continue without project info
                    }
                }

                if (!project) {
                    throw new Error('No project information available');
                }

                // Check if we're requesting a specific file in full
                // Note: filePath takes precedence over semantic query when both are provided
                // if (filePath) {
                //     console.log('Specific file requested:', filePath);
                //
                //     // Find the requested file
                //     const fileItem = fileManager.getFileItems().find(file => file.name === filePath.trim());
                //
                //     if (!fileItem) {
                //         throw new Error(`The requested file was not found: ${filePath}`);
                //     }
                //
                //     const fileItems = [fileItem];
                //
                //     // Create snippets for each file
                //     const snippets = fileItems.map(fileItem => ({
                //         filePath: fileItem.name,
                //         content: fileItem.content || '',
                //         startLine: 1,
                //         endLine: (fileItem.content || '').split('\n').length,
                //         type: 'unknown',
                //         symbols: [],
                //         score: 1.0
                //     }));
                //
                //     // Create a simplified result with just the full file contents
                //     const result = {
                //         query,
                //         snippets,
                //         codebaseStructure: { components: [], screens: [], contexts: [], hooks: [], utils: [], types: [], configs: [] },
                //         relationships: { imports: {}, exports: {}, dependencies: {}, dependents: {} }
                //     };
                //
                //     let message = `Showing full content of the requested file: ${fileItems[0].name}`;
                //
                //     return {
                //         result,
                //         message,
                //         toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')
                //     };
                // }
                
                // Initialize the context engine with the current files and project
                const contextEngine = new ContextEngine(fileManager.getFileItems(), project);

                // Default excluded files for Expo Snack projects if not provided
                const defaultExcludedFiles = [
                    // Large asset files
                    'assets/',
                    '.mp3',
                    '.mp4',
                    '.jpg',
                    '.png',
                    '.gif',
                    // Generated type files
                    'types/generated',
                    // These don't exist in Expo Snack but kept as fallbacks
                    'node_modules',
                    'dist'
                ];

                // Combine user-provided excludedFiles with defaults
                const combinedExcludedFiles = excludedFiles ?
                    [...excludedFiles, ...defaultExcludedFiles] :
                    defaultExcludedFiles;

                // Query the context engine
                const result = await contextEngine.query(query, reason, combinedExcludedFiles);

                // Format snippets as readable string instead of JSON array
                const formattedSnippets = formatSnippetsAsString(result.snippets || []);

                // Prepare a more informative response
                let message = `Found ${result.snippets?.length} snippets for your query.`;

                // Add information about excluded files if any
                if (excludedFiles && excludedFiles.length > 0) {
                    message += `\n${excludedFiles.length} files were excluded from the analysis.`;
                }

                if (result.actionPlan) {
                    message += `\n\nAction Plan: ${result.actionPlan.summary}\nComplexity: ${result.actionPlan.complexity}\nSteps: ${result.actionPlan.steps.length}`;
                }

                return {
                    result: formattedSnippets,
                    message,
                    toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')
                };
            } catch (error) {
                console.error('Context engine tool error:', error);
                return {
                    result: null,
                    message: `⚠️ Failed to query context engine: ${error instanceof Error ? error.message : 'Unknown error'}`
                }
            }
        },
    });
}
