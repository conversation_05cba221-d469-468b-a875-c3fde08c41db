import { tool } from 'ai';
import { z } from 'zod';
import { getProjectFunctionUrls } from '@/lib/db/function-deployment-queries';

/**
 * Tool for LLMs to get deployed function URLs for making API calls
 */
export const getFunctionUrlsTool = ({projectId}:{projectId: string}) => {
  return tool({
    description: 'Get the deployed function URLs for this project. Use this to get the correct URLs for making API calls to your deployed functions.',
    parameters: z.object({
      functionName: z.string().optional().describe('Optional: Get URL for specific function (e.g., "api", "auth", "analytics"). If not provided, returns all function URLs.')
    }),
    execute: async ({ functionName }: { functionName?: string }) => {
      try {
        console.log(`🔗 Getting function URLs for project: ${projectId}`);
        
        // Get all function URLs for the project
        const functionUrls = await getProjectFunctionUrls(projectId);
        
        if (Object.keys(functionUrls).length === 0) {
          return {
            success: false,
            message: 'No deployed functions found for this project. Create functions in the magically/functions/ folder and they will be automatically deployed.',
            availableFunctions: []
          };
        }
        
        // If specific function requested
        if (functionName) {
          const functionKey = `${functionName}_v1`;
          const url = functionUrls[functionKey];
          
          if (!url) {
            return {
              success: false,
              message: `Function "${functionName}" not found. Available functions: ${Object.keys(functionUrls).map(key => key.split('_')[0]).join(', ')}`,
              availableFunctions: Object.keys(functionUrls).map(key => key.split('_')[0])
            };
          }
          
          return {
            success: true,
            functionName,
            url,
            message: `Function "${functionName}" is deployed at: ${url}`
          };
        }
        
        // Return all function URLs
        const formattedUrls: Record<string, string> = {};
        const availableFunctions: string[] = [];
        
        Object.entries(functionUrls).forEach(([key, url]) => {
          const [name, version] = key.split('_');
          formattedUrls[name] = url;
          if (!availableFunctions.includes(name)) {
            availableFunctions.push(name);
          }
        });
        
        return {
          success: true,
          functionUrls: formattedUrls,
          availableFunctions,
          message: `Found ${availableFunctions.length} deployed functions: ${availableFunctions.join(', ')}`,
          usage: {
            example: `// Example usage in your code:
const API_URL = "${formattedUrls.api || 'https://your-api-url.deno.dev'}";
const response = await fetch(\`\${API_URL}/endpoint\`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ data: 'your data' })
});`
          }
        };
        
      } catch (error) {
        console.error('❌ Failed to get function URLs:', error);
        return {
          success: false,
          message: `Failed to get function URLs: ${error instanceof Error ? error.message : 'Unknown error'}`,
          availableFunctions: []
        };
      }
    }
  });
};
