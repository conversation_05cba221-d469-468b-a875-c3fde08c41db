import { eq, desc, and } from 'drizzle-orm';
import { instantDbApps, instantDbPlatformConfig, InstantDbApp, InstantDbPlatformConfig } from './schema';
import { db } from './db';

/**
 * Get InstantDB platform configuration (one-time setup)
 */
export async function getInstantDbPlatformConfig(): Promise<InstantDbPlatformConfig | null> {
  try {
    const [config] = await db
      .select()
      .from(instantDbPlatformConfig)
      .where(eq(instantDbPlatformConfig.isActive, true))
      .limit(1);
    
    return config || null;
  } catch (error) {
    console.error('Failed to get InstantDB platform config:', error);
    throw error;
  }
}

/**
 * Create or update InstantDB platform configuration
 */
export async function upsertInstantDbPlatformConfig(config: {
  clientId: string;
  clientSecret: string;
  accessToken?: string;
  refreshToken?: string;
  tokenExpiresAt?: Date;
}): Promise<InstantDbPlatformConfig> {
  try {
    // Deactivate existing configs
    await db
      .update(instantDbPlatformConfig)
      .set({ isActive: false, updatedAt: new Date() });

    // Insert new config
    const [newConfig] = await db
      .insert(instantDbPlatformConfig)
      .values({
        ...config,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return newConfig;
  } catch (error) {
    console.error('Failed to upsert InstantDB platform config:', error);
    throw error;
  }
}

/**
 * Update InstantDB platform tokens
 */
export async function updateInstantDbPlatformTokens(tokens: {
  accessToken: string;
  refreshToken: string;
  tokenExpiresAt: Date;
}): Promise<void> {
  try {
    await db
      .update(instantDbPlatformConfig)
      .set({
        ...tokens,
        lastRefreshedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(instantDbPlatformConfig.isActive, true));
  } catch (error) {
    console.error('Failed to update InstantDB platform tokens:', error);
    throw error;
  }
}

/**
 * Get InstantDB app by project ID
 */
export async function getInstantDbAppByProjectId(projectId: string): Promise<InstantDbApp | null> {
  try {
    const [app] = await db
      .select()
      .from(instantDbApps)
      .where(eq(instantDbApps.projectId, projectId))
      .limit(1);
    
    return app || null;
  } catch (error) {
    console.error('Failed to get InstantDB app by project ID:', error);
    throw error;
  }
}

/**
 * Create InstantDB app for project
 */
export async function createInstantDbApp(data: {
  projectId: string;
  instantDbAppId: string;
  appName: string;
  accessToken: string;
  refreshToken: string;
  tokenExpiresAt: Date;
  metadata?: {
    schema?: Record<string, any>;
    rules?: Record<string, any>;
    adminToken?: string;
  };
}): Promise<InstantDbApp> {
  try {
    const [app] = await db
      .insert(instantDbApps)
      .values({
        ...data,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return app;
  } catch (error) {
    console.error('Failed to create InstantDB app:', error);
    throw error;
  }
}

/**
 * Update InstantDB app tokens
 */
export async function updateInstantDbAppTokens(
  projectId: string,
  tokens: {
    accessToken: string;
    refreshToken: string;
    tokenExpiresAt: Date;
  }
): Promise<void> {
  try {
    await db
      .update(instantDbApps)
      .set({
        ...tokens,
        lastSyncedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(instantDbApps.projectId, projectId));
  } catch (error) {
    console.error('Failed to update InstantDB app tokens:', error);
    throw error;
  }
}

/**
 * Update InstantDB app status
 */
export async function updateInstantDbAppStatus(
  projectId: string,
  status: 'active' | 'inactive' | 'error'
): Promise<void> {
  try {
    await db
      .update(instantDbApps)
      .set({
        status,
        updatedAt: new Date(),
      })
      .where(eq(instantDbApps.projectId, projectId));
  } catch (error) {
    console.error('Failed to update InstantDB app status:', error);
    throw error;
  }
}

/**
 * Get all InstantDB apps with status
 */
export async function getAllInstantDbApps(): Promise<InstantDbApp[]> {
  try {
    const apps = await db
      .select()
      .from(instantDbApps)
      .orderBy(desc(instantDbApps.createdAt));
    
    return apps;
  } catch (error) {
    console.error('Failed to get all InstantDB apps:', error);
    throw error;
  }
}

/**
 * Delete InstantDB app
 */
export async function deleteInstantDbApp(projectId: string): Promise<void> {
  try {
    await db
      .delete(instantDbApps)
      .where(eq(instantDbApps.projectId, projectId));
  } catch (error) {
    console.error('Failed to delete InstantDB app:', error);
    throw error;
  }
}

/**
 * Check if InstantDB platform is configured
 */
export async function isInstantDbPlatformConfigured(): Promise<boolean> {
  try {
    const config = await getInstantDbPlatformConfig();
    return !!config && !!config.accessToken;
  } catch (error) {
    console.error('Failed to check InstantDB platform configuration:', error);
    return false;
  }
}

/**
 * Get InstantDB apps that need token refresh
 */
export async function getInstantDbAppsNeedingRefresh(): Promise<InstantDbApp[]> {
  try {
    const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);
    
    const apps = await db
      .select()
      .from(instantDbApps)
      .where(
        and(
          eq(instantDbApps.status, 'active'),
          // Token expires within 5 minutes or already expired
          // Note: This is a simplified check, you might want to use a proper date comparison
        )
      );
    
    return apps.filter(app => 
      !app.tokenExpiresAt || new Date(app.tokenExpiresAt) <= fiveMinutesFromNow
    );
  } catch (error) {
    console.error('Failed to get InstantDB apps needing refresh:', error);
    throw error;
  }
}
