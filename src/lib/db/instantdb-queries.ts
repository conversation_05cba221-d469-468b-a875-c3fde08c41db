import { eq } from 'drizzle-orm';
import { instantDbPlatformConfig, InstantDbPlatformConfig } from './schema';
import { db } from './db';

/**
 * Get InstantDB platform configuration (one-time setup)
 */
export async function getInstantDbPlatformConfig(): Promise<InstantDbPlatformConfig | null> {
  try {
    const [config] = await db
      .select()
      .from(instantDbPlatformConfig)
      .where(eq(instantDbPlatformConfig.isActive, true))
      .limit(1);

    return config || null;
  } catch (error) {
    console.error('Failed to get InstantDB platform config:', error);
    throw error;
  }
}

/**
 * Create or update InstantDB platform configuration
 */
export async function upsertInstantDbPlatformConfig(config: {
  clientId: string;
  clientSecret: string;
}): Promise<InstantDbPlatformConfig> {
  try {
    // Deactivate existing configs
    await db
      .update(instantDbPlatformConfig)
      .set({ isActive: false, updatedAt: new Date() });

    // Insert new config
    const [newConfig] = await db
      .insert(instantDbPlatformConfig)
      .values({
        ...config,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return newConfig;
  } catch (error) {
    console.error('Failed to upsert InstantDB platform config:', error);
    throw error;
  }
}

/**
 * Check if InstantDB platform is configured
 */
export async function isInstantDbPlatformConfigured(): Promise<boolean> {
  try {
    const config = await getInstantDbPlatformConfig();
    return !!config && !!config.clientId && !!config.clientSecret;
  } catch (error) {
    console.error('Failed to check InstantDB platform configuration:', error);
    return false;
  }
}
