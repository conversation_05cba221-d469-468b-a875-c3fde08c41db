CREATE TABLE "instant_db_platform_config" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"client_id" text NOT NULL,
	"client_secret" text NOT NULL,
	"access_token" text,
	"refresh_token" text,
	"token_expires_at" timestamp,
	"is_active" boolean DEFAULT false NOT NULL,
	"last_refreshed_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "Project" ADD COLUMN "instantDbAppId" text;--> statement-breakpoint
ALTER TABLE "Project" ADD COLUMN "instantDbAccessToken" text;--> statement-breakpoint
ALTER TABLE "Project" ADD COLUMN "instantDbRefreshToken" text;--> statement-breakpoint
ALTER TABLE "Project" ADD COLUMN "instantDbTokenExpiresAt" timestamp;