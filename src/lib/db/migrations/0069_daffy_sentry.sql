CREATE TABLE "function_deployments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"projectId" uuid NOT NULL,
	"functionName" text NOT NULL,
	"version" text DEFAULT 'v1' NOT NULL,
	"denoProjectId" text NOT NULL,
	"denoDeploymentId" text NOT NULL,
	"url" text NOT NULL,
	"isActive" boolean DEFAULT true NOT NULL,
	"status" varchar DEFAULT 'deploying' NOT NULL,
	"error" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "function_deployments" ADD CONSTRAINT "function_deployments_projectId_Project_id_fk" FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "function_deployments_project_function_idx" ON "function_deployments" USING btree ("projectId","functionName");--> statement-breakpoint
CREATE INDEX "function_deployments_active_idx" ON "function_deployments" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "function_deployments_status_idx" ON "function_deployments" USING btree ("status");--> statement-breakpoint
