import { relations } from "drizzle-orm/relations";
import { user, connection, passwordResetTokens, suggestion, document, project, projectConnection, chat, fileState, message, projectChat, deployments, apkBuilds, tokenConsumption, screenshotState, designScreen, temperatureOptimization, oauthAuthorizationCodes, oauthClients, oauthTokens, chatToProject, vote } from "./schema";

export const connectionRelations = relations(connection, ({one, many}) => ({
	user: one(user, {
		fields: [connection.userId],
		references: [user.id]
	}),
	chats: many(chat),
	projects: many(project),
}));

export const userRelations = relations(user, ({many}) => ({
	connections: many(connection),
	passwordResetTokens: many(passwordResetTokens),
	suggestions: many(suggestion),
	deployments: many(deployments),
	tokenConsumptions: many(tokenConsumption),
	chats: many(chat),
	messages: many(message),
	temperatureOptimizations: many(temperatureOptimization),
	projects: many(project),
	oauthAuthorizationCodes: many(oauthAuthorizationCodes),
	oauthTokens: many(oauthTokens),
	documents: many(document),
}));

export const passwordResetTokensRelations = relations(passwordResetTokens, ({one}) => ({
	user: one(user, {
		fields: [passwordResetTokens.userId],
		references: [user.id]
	}),
}));

export const suggestionRelations = relations(suggestion, ({one}) => ({
	user: one(user, {
		fields: [suggestion.userId],
		references: [user.id]
	}),
	document: one(document, {
		fields: [suggestion.documentId],
		references: [document.id]
	}),
}));

export const documentRelations = relations(document, ({one, many}) => ({
	suggestions: many(suggestion),
	user: one(user, {
		fields: [document.userId],
		references: [user.id]
	}),
}));

export const projectConnectionRelations = relations(projectConnection, ({one}) => ({
	project: one(project, {
		fields: [projectConnection.projectId],
		references: [project.id]
	}),
}));

export const projectRelations = relations(project, ({one, many}) => ({
	projectConnections: many(projectConnection),
	fileStates: many(fileState),
	projectChats: many(projectChat),
	deployments: many(deployments),
	chats: many(chat, {
		relationName: "chat_projectId_project_id"
	}),
	messages: many(message),
	screenshotStates: many(screenshotState),
	designScreens: many(designScreen),
	temperatureOptimizations: many(temperatureOptimization),
	user: one(user, {
		fields: [project.userId],
		references: [user.id]
	}),
	connection: one(connection, {
		fields: [project.connectionId],
		references: [connection.id]
	}),
	chat: one(chat, {
		fields: [project.designChatId],
		references: [chat.id],
		relationName: "project_designChatId_chat_id"
	}),
	oauthClients: many(oauthClients),
	chatToProjects: many(chatToProject),
	votes: many(vote),
}));

export const fileStateRelations = relations(fileState, ({one, many}) => ({
	chat: one(chat, {
		fields: [fileState.chatId],
		references: [chat.id]
	}),
	message: one(message, {
		fields: [fileState.messageId],
		references: [message.id]
	}),
	project: one(project, {
		fields: [fileState.projectId],
		references: [project.id]
	}),
	deployments: many(deployments),
}));

export const chatRelations = relations(chat, ({one, many}) => ({
	fileStates: many(fileState),
	projectChats: many(projectChat),
	user: one(user, {
		fields: [chat.userId],
		references: [user.id]
	}),
	connection: one(connection, {
		fields: [chat.connectionId],
		references: [connection.id]
	}),
	project: one(project, {
		fields: [chat.projectId],
		references: [project.id],
		relationName: "chat_projectId_project_id"
	}),
	messages: many(message),
	designScreens: many(designScreen),
	temperatureOptimizations: many(temperatureOptimization),
	projects: many(project, {
		relationName: "project_designChatId_chat_id"
	}),
	chatToProjects: many(chatToProject),
	votes: many(vote),
}));

export const messageRelations = relations(message, ({one, many}) => ({
	fileStates: many(fileState),
	chat: one(chat, {
		fields: [message.chatId],
		references: [chat.id]
	}),
	user: one(user, {
		fields: [message.userId],
		references: [user.id]
	}),
	project: one(project, {
		fields: [message.projectId],
		references: [project.id]
	}),
	temperatureOptimizations: many(temperatureOptimization),
	votes: many(vote),
}));

export const projectChatRelations = relations(projectChat, ({one}) => ({
	project: one(project, {
		fields: [projectChat.projectId],
		references: [project.id]
	}),
	chat: one(chat, {
		fields: [projectChat.chatId],
		references: [chat.id]
	}),
}));

export const deploymentsRelations = relations(deployments, ({one}) => ({
	user: one(user, {
		fields: [deployments.userId],
		references: [user.id]
	}),
	apkBuild: one(apkBuilds, {
		fields: [deployments.buildId],
		references: [apkBuilds.id]
	}),
	fileState: one(fileState, {
		fields: [deployments.fileStateId],
		references: [fileState.id]
	}),
	project: one(project, {
		fields: [deployments.projectId],
		references: [project.id]
	}),
}));

export const apkBuildsRelations = relations(apkBuilds, ({many}) => ({
	deployments: many(deployments),
}));

export const tokenConsumptionRelations = relations(tokenConsumption, ({one}) => ({
	user: one(user, {
		fields: [tokenConsumption.userId],
		references: [user.id]
	}),
}));

export const screenshotStateRelations = relations(screenshotState, ({one}) => ({
	project: one(project, {
		fields: [screenshotState.projectId],
		references: [project.id]
	}),
}));

export const designScreenRelations = relations(designScreen, ({one}) => ({
	chat: one(chat, {
		fields: [designScreen.chatId],
		references: [chat.id]
	}),
	project: one(project, {
		fields: [designScreen.projectId],
		references: [project.id]
	}),
}));

export const temperatureOptimizationRelations = relations(temperatureOptimization, ({one}) => ({
	message: one(message, {
		fields: [temperatureOptimization.messageId],
		references: [message.id]
	}),
	chat: one(chat, {
		fields: [temperatureOptimization.chatId],
		references: [chat.id]
	}),
	project: one(project, {
		fields: [temperatureOptimization.projectId],
		references: [project.id]
	}),
	user: one(user, {
		fields: [temperatureOptimization.userId],
		references: [user.id]
	}),
}));

export const oauthAuthorizationCodesRelations = relations(oauthAuthorizationCodes, ({one}) => ({
	user: one(user, {
		fields: [oauthAuthorizationCodes.userId],
		references: [user.id]
	}),
}));

export const oauthClientsRelations = relations(oauthClients, ({one}) => ({
	project: one(project, {
		fields: [oauthClients.appId],
		references: [project.id]
	}),
}));

export const oauthTokensRelations = relations(oauthTokens, ({one}) => ({
	user: one(user, {
		fields: [oauthTokens.userId],
		references: [user.id]
	}),
}));

export const chatToProjectRelations = relations(chatToProject, ({one}) => ({
	chat: one(chat, {
		fields: [chatToProject.chatId],
		references: [chat.id]
	}),
	project: one(project, {
		fields: [chatToProject.projectId],
		references: [project.id]
	}),
}));

export const voteRelations = relations(vote, ({one}) => ({
	chat: one(chat, {
		fields: [vote.chatId],
		references: [chat.id]
	}),
	message: one(message, {
		fields: [vote.messageId],
		references: [message.id]
	}),
	project: one(project, {
		fields: [vote.projectId],
		references: [project.id]
	}),
}));