{"id": "dac70d32-cd2e-4732-9f9e-bc85bdd1c982", "prevId": "d7fc4ba6-b077-4eaa-a1e9-31b8d752c669", "version": "7", "dialect": "postgresql", "tables": {"public.apk_builds": {"name": "apk_builds", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "apk_url": {"name": "apk_url", "type": "text", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Chat": {"name": "Cha<PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": false}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'private'"}, "connectionId": {"name": "connectionId", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'app'"}, "supabaseProjectId": {"name": "supabaseProjectId", "type": "text", "primaryKey": false, "notNull": false}, "supabaseAnonKey": {"name": "supabaseAnonKey", "type": "text", "primaryKey": false, "notNull": false}, "supabaseServiceKey": {"name": "supabaseServiceKey", "type": "text", "primaryKey": false, "notNull": false}, "isInitialized": {"name": "isInitialized", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "needsContinuation": {"name": "needsContinuation", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "designHtml": {"name": "designHtml", "type": "text", "primaryKey": false, "notNull": false}, "designStatus": {"name": "designStatus", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "isDesignApproved": {"name": "isDesignApproved", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"chat_userId_idx": {"name": "chat_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_projectId_idx": {"name": "chat_projectId_idx", "columns": [{"expression": "projectId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_userId_projectId_idx": {"name": "chat_userId_projectId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "projectId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_updatedAt_idx": {"name": "chat_updatedAt_idx", "columns": [{"expression": "updatedAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Chat_userId_User_id_fk": {"name": "Chat_userId_User_id_fk", "tableFrom": "Cha<PERSON>", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Chat_projectId_Project_id_fk": {"name": "Chat_projectId_Project_id_fk", "tableFrom": "Cha<PERSON>", "tableTo": "Project", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Chat_connectionId_Connection_id_fk": {"name": "Chat_connectionId_Connection_id_fk", "tableFrom": "Cha<PERSON>", "tableTo": "Connection", "columnsFrom": ["connectionId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Connection": {"name": "Connection", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "lastSyncedAt": {"name": "lastSyncedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Connection_userId_User_id_fk": {"name": "Connection_userId_User_id_fk", "tableFrom": "Connection", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.deployments": {"name": "deployments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "platform": {"name": "platform", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "versionCode": {"name": "versionCode", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'queued'"}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "buildId": {"name": "buildId", "type": "uuid", "primaryKey": false, "notNull": false}, "fileStateId": {"name": "fileStateId", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"slug_idx": {"name": "slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "slug_platform_idx": {"name": "slug_platform_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "platform", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"deployments_projectId_Project_id_fk": {"name": "deployments_projectId_Project_id_fk", "tableFrom": "deployments", "tableTo": "Project", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "deployments_userId_User_id_fk": {"name": "deployments_userId_User_id_fk", "tableFrom": "deployments", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "deployments_buildId_apk_builds_id_fk": {"name": "deployments_buildId_apk_builds_id_fk", "tableFrom": "deployments", "tableTo": "apk_builds", "columnsFrom": ["buildId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "deployments_fileStateId_FileState_id_fk": {"name": "deployments_fileStateId_FileState_id_fk", "tableFrom": "deployments", "tableTo": "FileState", "columnsFrom": ["fileStateId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"slug_unique": {"name": "slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.DesignScreen": {"name": "DesignScreen", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "html": {"name": "html", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "real", "primaryKey": false, "notNull": true, "default": 0}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'complete'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"design_screen_chatId_idx": {"name": "design_screen_chatId_idx", "columns": [{"expression": "chatId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "design_screen_projectId_idx": {"name": "design_screen_projectId_idx", "columns": [{"expression": "projectId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "design_screen_order_idx": {"name": "design_screen_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"DesignScreen_chatId_Chat_id_fk": {"name": "DesignScreen_chatId_Chat_id_fk", "tableFrom": "DesignScreen", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "DesignScreen_projectId_Project_id_fk": {"name": "DesignScreen_projectId_Project_id_fk", "tableFrom": "DesignScreen", "tableTo": "Project", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Document": {"name": "Document", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'text'"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Document_userId_User_id_fk": {"name": "Document_userId_User_id_fk", "tableFrom": "Document", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Document_id_createdAt_pk": {"name": "Document_id_createdAt_pk", "columns": ["id", "createdAt"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.FileEmbedding": {"name": "FileEmbedding", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "projectId": {"name": "projectId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "'default'"}, "filePath": {"name": "filePath", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "contentHash": {"name": "contentHash", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"file_embeddings_project_id_idx": {"name": "file_embeddings_project_id_idx", "columns": [{"expression": "projectId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"file_path_unique": {"name": "file_path_unique", "nullsNotDistinct": false, "columns": ["filePath"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.FileState": {"name": "FileState", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": false}, "sqlQuery": {"name": "sqlQuery", "type": "text", "primaryKey": false, "notNull": false}, "sqlStatus": {"name": "sqlStatus", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sqlError": {"name": "sqlError", "type": "text", "primaryKey": false, "notNull": false}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": false}, "files": {"name": "files", "type": "json", "primaryKey": false, "notNull": true}, "dependencies": {"name": "dependencies", "type": "json", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "is_base_cache_version": {"name": "is_base_cache_version", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"fileState_chatId_idx": {"name": "fileState_chatId_idx", "columns": [{"expression": "chatId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fileState_projectId_idx": {"name": "fileState_projectId_idx", "columns": [{"expression": "projectId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fileState_messageId_idx": {"name": "fileState_messageId_idx", "columns": [{"expression": "messageId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fileState_isBaseCacheVersion_idx": {"name": "fileState_isBaseCacheVersion_idx", "columns": [{"expression": "is_base_cache_version", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fileState_chatId_isBaseCacheVersion_idx": {"name": "fileState_chatId_isBaseCacheVersion_idx", "columns": [{"expression": "chatId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_base_cache_version", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fileState_version_idx": {"name": "fileState_version_idx", "columns": [{"expression": "version", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fileState_chatId_messageId_idx": {"name": "fileState_chatId_messageId_idx", "columns": [{"expression": "chatId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "messageId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fileState_createdAt_idx": {"name": "fileState_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"FileState_chatId_Chat_id_fk": {"name": "FileState_chatId_Chat_id_fk", "tableFrom": "FileState", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "FileState_projectId_Project_id_fk": {"name": "FileState_projectId_Project_id_fk", "tableFrom": "FileState", "tableTo": "Project", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "FileState_messageId_Message_id_fk": {"name": "FileState_messageId_Message_id_fk", "tableFrom": "FileState", "tableTo": "Message", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Message": {"name": "Message", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "remoteProvider": {"name": "remoteProvider", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remoteProviderId": {"name": "remoteProviderId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "json", "primaryKey": false, "notNull": true}, "componentContexts": {"name": "componentContexts", "type": "json", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "autoFixed": {"name": "autoFixed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "parts": {"name": "parts", "type": "json", "primaryKey": false, "notNull": false}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "attachments": {"name": "attachments", "type": "json", "primaryKey": false, "notNull": false}, "hidden": {"name": "hidden", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": false}, "finishReason": {"name": "finishReason", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "parentUserMessageId": {"name": "parentUserMessageId", "type": "uuid", "primaryKey": false, "notNull": false}, "parentAssistantMessageId": {"name": "parentAssistantMessageId", "type": "uuid", "primaryKey": false, "notNull": false}, "isAssistantGroupHead": {"name": "isAssistantGroupHead", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"message_chatId_idx": {"name": "message_chatId_idx", "columns": [{"expression": "chatId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "message_projectId_idx": {"name": "message_projectId_idx", "columns": [{"expression": "projectId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "message_userId_idx": {"name": "message_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "message_chatId_createdAt_idx": {"name": "message_chatId_createdAt_idx", "columns": [{"expression": "chatId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "message_role_idx": {"name": "message_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "message_parentAssistantMessageId_idx": {"name": "message_parentAssistantMessageId_idx", "columns": [{"expression": "parentAssistantMessageId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Message_chatId_Chat_id_fk": {"name": "Message_chatId_Chat_id_fk", "tableFrom": "Message", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Message_projectId_Project_id_fk": {"name": "Message_projectId_Project_id_fk", "tableFrom": "Message", "tableTo": "Project", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Message_userId_User_id_fk": {"name": "Message_userId_User_id_fk", "tableFrom": "Message", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.oauth_authorization_codes": {"name": "oauth_authorization_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "redirect_uri": {"name": "redirect_uri", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "code_challenge": {"name": "code_challenge", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "code_challenge_method": {"name": "code_challenge_method", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"oauth_auth_codes_code_idx": {"name": "oauth_auth_codes_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "oauth_auth_codes_client_id_idx": {"name": "oauth_auth_codes_client_id_idx", "columns": [{"expression": "client_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "oauth_auth_codes_user_id_idx": {"name": "oauth_auth_codes_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "oauth_auth_codes_expires_at_idx": {"name": "oauth_auth_codes_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"oauth_authorization_codes_user_id_User_id_fk": {"name": "oauth_authorization_codes_user_id_User_id_fk", "tableFrom": "oauth_authorization_codes", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"oauth_authorization_codes_code_unique": {"name": "oauth_authorization_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.oauth_clients": {"name": "oauth_clients", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "client_secret": {"name": "client_secret", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "app_id": {"name": "app_id", "type": "uuid", "primaryKey": false, "notNull": false}, "redirect_uri": {"name": "redirect_uri", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"oauth_clients_client_id_idx": {"name": "oauth_clients_client_id_idx", "columns": [{"expression": "client_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "oauth_clients_app_id_idx": {"name": "oauth_clients_app_id_idx", "columns": [{"expression": "app_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"oauth_clients_app_id_Project_id_fk": {"name": "oauth_clients_app_id_Project_id_fk", "tableFrom": "oauth_clients", "tableTo": "Project", "columnsFrom": ["app_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"oauth_clients_client_id_unique": {"name": "oauth_clients_client_id_unique", "nullsNotDistinct": false, "columns": ["client_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.oauth_tokens": {"name": "oauth_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "access_token": {"name": "access_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "'read write'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"oauth_tokens_access_token_idx": {"name": "oauth_tokens_access_token_idx", "columns": [{"expression": "access_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "oauth_tokens_refresh_token_idx": {"name": "oauth_tokens_refresh_token_idx", "columns": [{"expression": "refresh_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "oauth_tokens_client_id_idx": {"name": "oauth_tokens_client_id_idx", "columns": [{"expression": "client_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "oauth_tokens_user_id_idx": {"name": "oauth_tokens_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "oauth_tokens_expires_at_idx": {"name": "oauth_tokens_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"oauth_tokens_user_id_User_id_fk": {"name": "oauth_tokens_user_id_User_id_fk", "tableFrom": "oauth_tokens", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"oauth_tokens_access_token_unique": {"name": "oauth_tokens_access_token_unique", "nullsNotDistinct": false, "columns": ["access_token"]}, "oauth_tokens_refresh_token_unique": {"name": "oauth_tokens_refresh_token_unique", "nullsNotDistinct": false, "columns": ["refresh_token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.password_reset_tokens": {"name": "password_reset_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"password_reset_tokens_user_id_User_id_fk": {"name": "password_reset_tokens_user_id_User_id_fk", "tableFrom": "password_reset_tokens", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"password_reset_tokens_token_unique": {"name": "password_reset_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ProjectChat": {"name": "ProjectChat", "schema": "", "columns": {"projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": true}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "isPrimary": {"name": "isPrimary", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ProjectChat_projectId_Project_id_fk": {"name": "ProjectChat_projectId_Project_id_fk", "tableFrom": "ProjectChat", "tableTo": "Project", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ProjectChat_chatId_Chat_id_fk": {"name": "ProjectChat_chatId_Chat_id_fk", "tableFrom": "ProjectChat", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ProjectConnection": {"name": "ProjectConnection", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "providerProjectId": {"name": "providerProjectId", "type": "text", "primaryKey": false, "notNull": true}, "providerProjectData": {"name": "providerProjectData", "type": "json", "primaryKey": false, "notNull": false}, "types": {"name": "types", "type": "json", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ProjectConnection_projectId_Project_id_fk": {"name": "ProjectConnection_projectId_Project_id_fk", "tableFrom": "ProjectConnection", "tableTo": "Project", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Project": {"name": "Project", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "appName": {"name": "appName", "type": "text", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "scheme": {"name": "scheme", "type": "text", "primaryKey": false, "notNull": false}, "bundleIdentifier": {"name": "bundleIdentifier", "type": "text", "primaryKey": false, "notNull": false}, "packageName": {"name": "packageName", "type": "text", "primaryKey": false, "notNull": false}, "isMigratedv1": {"name": "isMigratedv1", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "splashImage": {"name": "splashImage", "type": "text", "primaryKey": false, "notNull": false}, "primaryColor": {"name": "primaryColor", "type": "text", "primaryKey": false, "notNull": false, "default": "'#000000'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "privacyPolicyUrl": {"name": "privacyPolicyUrl", "type": "text", "primaryKey": false, "notNull": false}, "termsOfServiceUrl": {"name": "termsOfServiceUrl", "type": "text", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "connectionId": {"name": "connectionId", "type": "uuid", "primaryKey": false, "notNull": false}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'private'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": false}, "initialUXGuidelines": {"name": "initialUXGuidelines", "type": "text", "primaryKey": false, "notNull": false}, "knowledgeBase": {"name": "knowledgeBase", "type": "text", "primaryKey": false, "notNull": false}, "aiGeneratedMemory": {"name": "aiGeneratedMemory", "type": "text", "primaryKey": false, "notNull": false}, "approvedDesignHtml": {"name": "approvedDesignHtml", "type": "text", "primaryKey": false, "notNull": false}, "designChatId": {"name": "designChatId", "type": "uuid", "primaryKey": false, "notNull": false}, "supabaseProjectId": {"name": "supabaseProjectId", "type": "text", "primaryKey": false, "notNull": false}, "supabaseAnonKey": {"name": "supabaseAnonKey", "type": "text", "primaryKey": false, "notNull": false}, "supabaseServiceKey": {"name": "supabaseServiceKey", "type": "text", "primaryKey": false, "notNull": false}, "convexProjectId": {"name": "convexProjectId", "type": "text", "primaryKey": false, "notNull": false}, "convexDeploymentUrl": {"name": "convexDeploymentUrl", "type": "text", "primaryKey": false, "notNull": false}, "convexDeploymentKey": {"name": "convexDeploymentKey", "type": "text", "primaryKey": false, "notNull": false}, "convexTeam": {"name": "convexTeam", "type": "text", "primaryKey": false, "notNull": false}, "mongodbAtlasAppId": {"name": "mongodbAtlasAppId", "type": "text", "primaryKey": false, "notNull": false}, "mongodbAtlasUrl": {"name": "mongodbAtlasUrl", "type": "text", "primaryKey": false, "notNull": false}, "mongodbAtlasProjectId": {"name": "mongodbAtlasProjectId", "type": "text", "primaryKey": false, "notNull": false}, "mongodbAtlasDatabaseName": {"name": "mongodbAtlasDatabaseName", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Project_userId_User_id_fk": {"name": "Project_userId_User_id_fk", "tableFrom": "Project", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "Project_connectionId_Connection_id_fk": {"name": "Project_connectionId_Connection_id_fk", "tableFrom": "Project", "tableTo": "Connection", "columnsFrom": ["connectionId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "Project_designChatId_Chat_id_fk": {"name": "Project_designChatId_Chat_id_fk", "tableFrom": "Project", "tableTo": "Cha<PERSON>", "columnsFrom": ["designChatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ScreenshotState": {"name": "ScreenshotState", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": false}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": false}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": false}, "screenshots": {"name": "screenshots", "type": "json", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"screenshotState_projectId_idx": {"name": "screenshotState_projectId_idx", "columns": [{"expression": "projectId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "screenshotState_chatId_idx": {"name": "screenshotState_chatId_idx", "columns": [{"expression": "chatId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "screenshotState_messageId_idx": {"name": "screenshotState_messageId_idx", "columns": [{"expression": "messageId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "screenshotState_createdAt_idx": {"name": "screenshotState_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ScreenshotState_projectId_Project_id_fk": {"name": "ScreenshotState_projectId_Project_id_fk", "tableFrom": "ScreenshotState", "tableTo": "Project", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Stream": {"name": "Stream", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"Stream_id_pk": {"name": "Stream_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Subscription": {"name": "Subscription", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'inactive'"}, "planId": {"name": "planId", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'free'"}, "subscriptionId": {"name": "subscriptionId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 50}, "creditsUsed": {"name": "creditsUsed", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "lemonSqueezyCustomerId": {"name": "lemonSqueezyCustomerId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "lemonSqueezySubscriptionId": {"name": "lemonSqueezySubscriptionId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "lemonSqueezyOrderId": {"name": "lemonSqueezyOrderId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "lemonSqueezyVariantId": {"name": "lemonSqueezyVariantId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "stripeCustomerId": {"name": "stripeCustomerId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "stripeSubscriptionId": {"name": "stripeSubscriptionId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "stripePriceId": {"name": "stripePriceId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "stripeCurrentPeriodEnd": {"name": "stripeCurrentPeriodEnd", "type": "timestamp", "primaryKey": false, "notNull": false}, "stripeVariantId": {"name": "stripeVariantId", "type": "timestamp", "primaryKey": false, "notNull": false}, "resetDate": {"name": "resetDate", "type": "timestamp", "primaryKey": false, "notNull": false}, "isDowngraded": {"name": "isDowngraded", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"subscription_userId_idx": {"name": "subscription_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscription_status_idx": {"name": "subscription_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscription_userId_updatedAt_idx": {"name": "subscription_userId_updatedAt_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "updatedAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscription_isActive_idx": {"name": "subscription_isActive_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Subscription_subscriptionId_unique": {"name": "Subscription_subscriptionId_unique", "nullsNotDistinct": false, "columns": ["subscriptionId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Suggestion": {"name": "Suggestion", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "documentId": {"name": "documentId", "type": "uuid", "primaryKey": false, "notNull": true}, "documentCreatedAt": {"name": "documentCreatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "originalText": {"name": "originalText", "type": "text", "primaryKey": false, "notNull": true}, "suggestedText": {"name": "suggestedText", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isResolved": {"name": "isResolved", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Suggestion_userId_User_id_fk": {"name": "Suggestion_userId_User_id_fk", "tableFrom": "Suggestion", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk": {"name": "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk", "tableFrom": "Suggestion", "tableTo": "Document", "columnsFrom": ["documentId", "documentCreatedAt"], "columnsTo": ["id", "createdAt"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Suggestion_id_pk": {"name": "Suggestion_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.TemperatureOptimization": {"name": "TemperatureOptimization", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": false}, "optimizedTemperature": {"name": "optimizedTemperature", "type": "real", "primaryKey": false, "notNull": true}, "selectedModel": {"name": "selected<PERSON><PERSON>l", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "reasoning": {"name": "reasoning", "type": "text", "primaryKey": false, "notNull": true}, "contextFactors": {"name": "contextFactors", "type": "json", "primaryKey": false, "notNull": true}, "userProgression": {"name": "userProgression", "type": "json", "primaryKey": false, "notNull": false}, "fileCount": {"name": "fileCount", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "optimizationDuration": {"name": "optimizationDuration", "type": "integer", "primaryKey": false, "notNull": false}, "wasSuccessful": {"name": "wasSuccessful", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "errorMessage": {"name": "errorMessage", "type": "text", "primaryKey": false, "notNull": false}, "usedFallback": {"name": "usedFallback", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "fallbackReason": {"name": "fallbackReason", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"temperature_optimization_messageId_idx": {"name": "temperature_optimization_messageId_idx", "columns": [{"expression": "messageId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "temperature_optimization_chatId_idx": {"name": "temperature_optimization_chatId_idx", "columns": [{"expression": "chatId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "temperature_optimization_projectId_idx": {"name": "temperature_optimization_projectId_idx", "columns": [{"expression": "projectId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "temperature_optimization_userId_idx": {"name": "temperature_optimization_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "temperature_optimization_createdAt_idx": {"name": "temperature_optimization_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "temperature_optimization_temperature_idx": {"name": "temperature_optimization_temperature_idx", "columns": [{"expression": "optimizedTemperature", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "temperature_optimization_successful_idx": {"name": "temperature_optimization_successful_idx", "columns": [{"expression": "wasSuccessful", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"TemperatureOptimization_messageId_Message_id_fk": {"name": "TemperatureOptimization_messageId_Message_id_fk", "tableFrom": "TemperatureOptimization", "tableTo": "Message", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "TemperatureOptimization_chatId_Chat_id_fk": {"name": "TemperatureOptimization_chatId_Chat_id_fk", "tableFrom": "TemperatureOptimization", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "TemperatureOptimization_projectId_Project_id_fk": {"name": "TemperatureOptimization_projectId_Project_id_fk", "tableFrom": "TemperatureOptimization", "tableTo": "Project", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "TemperatureOptimization_userId_User_id_fk": {"name": "TemperatureOptimization_userId_User_id_fk", "tableFrom": "TemperatureOptimization", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.TokenConsumption": {"name": "TokenConsumption", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "totalTimeToken": {"name": "totalTimeToken", "type": "real", "primaryKey": false, "notNull": true}, "promptTokens": {"name": "promptTokens", "type": "integer", "primaryKey": false, "notNull": true}, "completionTokens": {"name": "completionTokens", "type": "integer", "primaryKey": false, "notNull": true}, "totalTokens": {"name": "totalTokens", "type": "integer", "primaryKey": false, "notNull": true}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": true}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "inputCost": {"name": "inputCost", "type": "real", "primaryKey": false, "notNull": true}, "outputCost": {"name": "outputCost", "type": "real", "primaryKey": false, "notNull": true}, "cachingDiscount": {"name": "cachingDiscount", "type": "real", "primaryKey": false, "notNull": false}, "cacheDiscountPercent": {"name": "cacheDiscountPercent", "type": "real", "primaryKey": false, "notNull": false}, "subtotal": {"name": "subtotal", "type": "real", "primaryKey": false, "notNull": false}, "totalCost": {"name": "totalCost", "type": "real", "primaryKey": false, "notNull": true}, "isAnonymous": {"name": "isAnonymous", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "remoteProvider": {"name": "remoteProvider", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remoteProviderId": {"name": "remoteProviderId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "creditsConsumed": {"name": "creditsConsumed", "type": "integer", "primaryKey": false, "notNull": false}, "discountedCredits": {"name": "discountedCredits", "type": "integer", "primaryKey": false, "notNull": false}, "discountReason": {"name": "discountReason", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "errorId": {"name": "errorId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "discounted": {"name": "discounted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "isAutoFixed": {"name": "isAutoFixed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"tokenConsumption_userId_idx": {"name": "tokenConsumption_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "tokenConsumption_chatId_idx": {"name": "tokenConsumption_chatId_idx", "columns": [{"expression": "chatId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "tokenConsumption_projectId_idx": {"name": "tokenConsumption_projectId_idx", "columns": [{"expression": "projectId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "tokenConsumption_userId_createdAt_idx": {"name": "tokenConsumption_userId_createdAt_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "tokenConsumption_isAnonymous_idx": {"name": "tokenConsumption_isAnonymous_idx", "columns": [{"expression": "isAnonymous", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"TokenConsumption_userId_User_id_fk": {"name": "TokenConsumption_userId_User_id_fk", "tableFrom": "TokenConsumption", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.User": {"name": "User", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "firstName": {"name": "firstName", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "lastName": {"name": "lastName", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "linkedin": {"name": "linkedin", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'credentials'"}, "isAnonymous": {"name": "isAnonymous", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Vote": {"name": "Vote", "schema": "", "columns": {"chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": false}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "isUpvoted": {"name": "isUpvoted", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Vote_chatId_Chat_id_fk": {"name": "Vote_chatId_Chat_id_fk", "tableFrom": "Vote", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Vote_projectId_Project_id_fk": {"name": "Vote_projectId_Project_id_fk", "tableFrom": "Vote", "tableTo": "Project", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Vote_messageId_Message_id_fk": {"name": "Vote_messageId_Message_id_fk", "tableFrom": "Vote", "tableTo": "Message", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Vote_chatId_messageId_pk": {"name": "Vote_chatId_messageId_pk", "columns": ["chatId", "messageId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}