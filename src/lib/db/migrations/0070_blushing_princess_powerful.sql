ALTER TABLE "function_deployments" RENAME TO "FunctionDeployments";--> statement-breakpoint
ALTER TABLE "FunctionDeployments" DROP CONSTRAINT "function_deployments_projectId_Project_id_fk";
--> statement-breakpoint
ALTER TABLE "FunctionDeployments" ADD CONSTRAINT "FunctionDeployments_projectId_Project_id_fk" FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") ON DELETE no action ON UPDATE no action;