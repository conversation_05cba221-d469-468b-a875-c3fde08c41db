import { eq, desc, ne, and } from 'drizzle-orm';
import {projects, chat, fileState, Project} from './schema';
import { db } from './db';

/**
 * Get a project by its ID
 */
export async function getProjectById({id}: {id: string}): Promise<Project> {
  try {
    const [project] = await db.select().from(projects).where(eq(projects.id, id));
    return project as Project;
  } catch (error) {
    console.error('Failed to get project by id from database');
    throw error;
  }


}/**
 * Get a project by its ID
 */
export async function getSanitizedProjectById({id}: {id: string}): Promise<Project> {
  try {
    const [project] = await db.select().from(projects).where(eq(projects.id, id));
      const {supabaseAnonKey, supabaseServiceKey, ...rest} = project;
      return rest as Project;
  } catch (error) {
    console.error('Failed to get project by id from database');
    throw error;
  }
}

/**
 * Update project metadata
 */
export async function updateProjectMetadata(
  id: string,
  metadata: {
    slug?: string;
    appName?: string;
    description?: string;
    primaryColor?: string;
    bundleIdentifier?: string;
    packageName?: string;
    appIdentifier?: string;
    icon?: string;
    splashImage?: string;
    isMigratedv1?: boolean;
  }
) {
  try {
    const updateData = {
      ...metadata,
      updatedAt: new Date()
    };

    const [updatedProject] = await db
      .update(projects)
      .set(updateData)
      .where(eq(projects.id, id))
      .returning();
    
    return updatedProject;
  } catch (error) {
    console.error('Failed to update project metadata in database');
    throw error;
  }
}

/**
 * Check if user owns a project
 */
export async function isProjectOwner(projectId: string, userId: string) {
  try {
    const project = await getProjectById({id: projectId});
    return project && project.userId === userId;
  } catch (error) {
    console.error('Failed to check project ownership');
    throw error;
  }
}

/**
 * Get all projects for a user
 */
export async function getUserProjects(userId: string) {
  try {
    const userProjects = await db
      .select()
      .from(projects)
      .where(and(
          eq(projects.userId, userId),
          ne(projects.visibility, 'hidden')
      ))
      .orderBy(desc(projects.updatedAt));

    return userProjects;
  } catch (error) {
    console.error('Failed to get user projects from database');
    throw error;
  }
}

/**
 * Update project with InstantDB app information
 */
export async function updateProjectInstantDB(
  projectId: string,
  instantDbData: {
    instantDbAppId: string;
    instantDbAppTitle: string;
    instantDbCreatedAt: Date;
  }
): Promise<Project> {
  try {
    const [updatedProject] = await db
      .update(projects)
      .set({
        ...instantDbData,
        updatedAt: new Date()
      })
      .where(eq(projects.id, projectId))
      .returning();

    return updatedProject;
  } catch (error) {
    console.error('Failed to update project InstantDB data');
    throw error;
  }
}
