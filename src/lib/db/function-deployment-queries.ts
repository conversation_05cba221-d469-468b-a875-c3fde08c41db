import { eq, and, desc } from 'drizzle-orm';
import { db } from '@/lib/db';
import { functionDeployments, type FunctionDeployment } from './schema';

/**
 * Create a new function deployment record
 */
export async function createFunctionDeployment(data: {
  projectId: string;
  functionName: string;
  version: string;
  denoProjectId: string;
  denoDeploymentId: string;
  url: string;
  status?: 'deploying' | 'active' | 'failed' | 'inactive';
}): Promise<FunctionDeployment> {
  const [deployment] = await db
    .insert(functionDeployments)
    .values({
      ...data,
      status: data.status || 'deploying',
      isActive: true,
    })
    .returning();
  
  return deployment;
}

/**
 * Update function deployment status
 */
export async function updateFunctionDeploymentStatus(
  deploymentId: string,
  status: 'deploying' | 'active' | 'failed' | 'inactive',
  error?: string
): Promise<FunctionDeployment | null> {
  const [deployment] = await db
    .update(functionDeployments)
    .set({
      status,
      error,
      updatedAt: new Date(),
    })
    .where(eq(functionDeployments.id, deploymentId))
    .returning();
  
  return deployment || null;
}

/**
 * Get active deployment for a function
 */
export async function getActiveFunctionDeployment(
  projectId: string,
  functionName: string,
  version: string = 'v1'
): Promise<FunctionDeployment | null> {
  const [deployment] = await db
    .select()
    .from(functionDeployments)
    .where(
      and(
        eq(functionDeployments.projectId, projectId),
        eq(functionDeployments.functionName, functionName),
        eq(functionDeployments.version, version),
        eq(functionDeployments.isActive, true),
        eq(functionDeployments.status, 'active')
      )
    )
    .orderBy(desc(functionDeployments.createdAt))
    .limit(1);
  
  return deployment || null;
}

/**
 * Get all deployments for a project
 */
export async function getProjectFunctionDeployments(
  projectId: string
): Promise<FunctionDeployment[]> {
  return db
    .select()
    .from(functionDeployments)
    .where(eq(functionDeployments.projectId, projectId))
    .orderBy(desc(functionDeployments.createdAt));
}

/**
 * Get all active function URLs for a project (for LLM)
 */
export async function getProjectFunctionUrls(
  projectId: string
): Promise<Record<string, string>> {
  const deployments = await db
    .select({
      functionName: functionDeployments.functionName,
      version: functionDeployments.version,
      url: functionDeployments.url,
    })
    .from(functionDeployments)
    .where(
      and(
        eq(functionDeployments.projectId, projectId),
        eq(functionDeployments.isActive, true),
        eq(functionDeployments.status, 'active')
      )
    );
  
  const urls: Record<string, string> = {};
  deployments.forEach(deployment => {
    const key = `${deployment.functionName}_${deployment.version}`;
    urls[key] = deployment.url;
  });
  
  return urls;
}

/**
 * Deactivate old deployments when creating a new one
 */
export async function deactivateOldDeployments(
  projectId: string,
  functionName: string,
  version: string,
  excludeDeploymentId?: string
): Promise<void> {
  const whereCondition = excludeDeploymentId
    ? and(
        eq(functionDeployments.projectId, projectId),
        eq(functionDeployments.functionName, functionName),
        eq(functionDeployments.version, version),
        eq(functionDeployments.isActive, true)
      )
    : and(
        eq(functionDeployments.projectId, projectId),
        eq(functionDeployments.functionName, functionName),
        eq(functionDeployments.version, version),
        eq(functionDeployments.isActive, true)
      );

  await db
    .update(functionDeployments)
    .set({
      isActive: false,
      status: 'inactive',
      updatedAt: new Date(),
    })
    .where(whereCondition);
}

/**
 * Get deployment history for a function
 */
export async function getFunctionDeploymentHistory(
  projectId: string,
  functionName: string
): Promise<FunctionDeployment[]> {
  return db
    .select()
    .from(functionDeployments)
    .where(
      and(
        eq(functionDeployments.projectId, projectId),
        eq(functionDeployments.functionName, functionName)
      )
    )
    .orderBy(desc(functionDeployments.createdAt));
}
