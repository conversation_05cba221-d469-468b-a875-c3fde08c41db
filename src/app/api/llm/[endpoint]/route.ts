import { NextRequest, NextResponse } from 'next/server';
import { LLMAPIService } from '@/lib/services/llm-api-service';



// CORS headers
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-app-id, x-origin-url, x-socket-id',
    'Access-Control-Max-Age': '86400',
};

// Handle OPTIONS request (preflight)
export async function OPTIONS(request: NextRequest, { params }: {
    params: Promise<{ endpoint: string }>
}) {
    return new NextResponse(null, {
        status: 204,
        headers: corsHeaders,
    });
}

// Handle GET request for models endpoint
export async function GET(request: NextRequest, { params }: {
    params: Promise<{ endpoint: string }>
}) {
    const { endpoint } = await params;

    if (endpoint === 'models') {
        try {
            const models = LLMAPIService.getAvailableModels();
            return NextResponse.json(models, { headers: corsHeaders });
        } catch (error) {
            console.error('Error getting models:', error);
            return NextResponse.json(
                { error: 'Failed to get available models' },
                { status: 500, headers: corsHeaders }
            );
        }
    }

    return NextResponse.json(
        { error: 'Endpoint not found' },
        { status: 404, headers: corsHeaders }
    );
}

export async function POST(request: NextRequest, { params }: {
    params: Promise<{ endpoint: string }>
}) {
    const { endpoint } = await params;

    try {
        const body = await request.json();

        // Handle /api/llm/invoke endpoint
        if (endpoint === 'invoke') {
            try {
                const result = await LLMAPIService.handleInvoke(body);
                return NextResponse.json(result, { headers: corsHeaders });
            } catch (error: any) {
                return NextResponse.json(
                    { error: error.error || 'Failed to generate response' },
                    { status: error.status || 500, headers: corsHeaders }
                );
            }
        }

        // Handle /api/llm/chat endpoint
        if (endpoint === 'chat') {
            try {
                const result = await LLMAPIService.handleChat(body);

                // Handle streaming response
                if (result.stream) {
                    return result.stream.toDataStreamResponse({
                        headers: corsHeaders,
                    });
                }

                return NextResponse.json(result, { headers: corsHeaders });
            } catch (error: any) {
                return NextResponse.json(
                    { error: error.error || 'Failed to generate chat response' },
                    { status: error.status || 500, headers: corsHeaders }
                );
            }
        }

        // Handle /api/llm/image endpoint
        if (endpoint === 'image') {
            try {
                const result = await LLMAPIService.handleImage(body);
                return NextResponse.json(result, { headers: corsHeaders });
            } catch (error: any) {
                return NextResponse.json(
                    { error: error.error || 'Failed to generate image' },
                    { status: error.status || 500, headers: corsHeaders }
                );
            }
        }

        return NextResponse.json(
            { error: 'Endpoint not found' },
            { status: 404, headers: corsHeaders }
        );

    } catch (error) {
        console.error('Error parsing request body:', error);
        return NextResponse.json(
            { error: 'Invalid request body' },
            { status: 400, headers: corsHeaders }
        );
    }
}
