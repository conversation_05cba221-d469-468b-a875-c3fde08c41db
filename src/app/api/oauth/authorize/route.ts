import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { db } from '@/lib/db/db';
import { oauthClients, oauthAuthorizationCodes } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { nanoid } from 'nanoid';

/**
 * OAuth Authorization Endpoint
 * Handles the authorization flow for generated apps
 * 
 * Flow:
 * 1. Generated app redirects user here with client_id, redirect_uri, etc.
 * 2. If user not logged in, redirect to login
 * 3. If user logged in, generate authorization code
 * 4. Redirect back to app with code
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const clientId = searchParams.get('client_id');
    const redirectUri = searchParams.get('redirect_uri');
    const responseType = searchParams.get('response_type');
    const state = searchParams.get('state');
    const codeChallenge = searchParams.get('code_challenge');
    const codeChallengeMethod = searchParams.get('code_challenge_method');

    // Validate required parameters
    if (!clientId || !redirectUri || responseType !== 'code') {
      return NextResponse.json(
        { error: 'invalid_request', error_description: 'Missing or invalid parameters' },
        { status: 400 }
      );
    }

    // Verify client_id exists and redirect_uri matches
    const [client] = await db
      .select()
      .from(oauthClients)
      .where(eq(oauthClients.clientId, clientId))
      .limit(1);

    if (!client) {
      return NextResponse.json(
        { error: 'invalid_client', error_description: 'Invalid client_id' },
        { status: 400 }
      );
    }

    // Validate redirect URI using patterns for flexibility with Expo/React Native apps
    if (!isValidRedirectUri(redirectUri)) {
      console.log('Invalid redirectUri pattern:', redirectUri, 'for client:', client.clientId);
      return NextResponse.json(
        { error: 'invalid_request', error_description: 'Invalid redirect_uri pattern' },
        { status: 400 }
      );
    }

    // Check if user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      // User not logged in - redirect to login with return URL
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('callbackUrl', request.url);
      return NextResponse.redirect(loginUrl);
    }

    // User is authenticated - generate authorization code
    const authCode = nanoid(32);
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Store authorization code
    await db.insert(oauthAuthorizationCodes).values({
      code: authCode,
      clientId,
      userId: session.user.id,
      redirectUri,
      expiresAt,
      codeChallenge,
      codeChallengeMethod,
      state,
    });

    // Redirect back to app with authorization code
    const callbackUrl = new URL(redirectUri);
    callbackUrl.searchParams.set('code', authCode);
    if (state) {
      callbackUrl.searchParams.set('state', state);
    }

    console.log('callbackUrl', callbackUrl, clientId)
    return NextResponse.redirect(callbackUrl);
  } catch (error) {
    console.error('OAuth authorize error:', error);
    return NextResponse.json(
      { error: 'server_error', error_description: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Validate redirect URI using secure patterns
 * Allows flexibility for Expo/React Native apps while maintaining security
 */
function isValidRedirectUri(uri: string): boolean {
  try {
    const url = new URL(uri);

    // Allowed patterns for redirect URIs
    const allowedPatterns = [
      // Expo development and published apps
      /^exp:\/\/.*/, // exp://*************:19000/--/auth
      /^.*\.exp\.direct\/.*/, // https://exp-shell-app-assets.s3.us-west-1.amazonaws.com/...
      /^https:\/\/.*\.expo\.dev\/.*/, // https://snack-web-player.s3.us-west-1.amazonaws.com/...

      // Convex apps
      /^https:\/\/.*\.convex\.cloud\/.*/, // https://app123.convex.cloud/auth/callback

      // Magically.life callback URLs (centralized OAuth callback)
      /^https:\/\/magically\.life\/projects\/.*\/callback\/.*/, // https://magically.life/projects/PROJECT_ID/callback/magically
      /^http:\/\/localhost:3000\/projects\/.*\/callback\/.*/, // Local development

      // Local development
      /^http:\/\/localhost:\d+\/.*/, // http://localhost:3000/auth/callback
      /^http:\/\/127\.0\.0\.1:\d+\/.*/, // http://127.0.0.1:3000/auth/callback
      /^http:\/\/192\.168\.\d+\.\d+:\d+\/.*/, // http://*************:3000/auth/callback

      // Expo Snack
      /^https:\/\/snack-web-player\.s3\..*\.amazonaws\.com\/.*/, // Snack web player
      /^https:\/\/.*\.snack\.expo\.dev\/.*/, // New Snack domains

      // Custom schemes for mobile apps (if needed)
      /^[a-zA-Z][a-zA-Z0-9+.-]*:\/\/.*/, // Custom URL schemes like myapp://auth
    ];

    // Check if URI matches any allowed pattern
    const isPatternMatch = allowedPatterns.some(pattern => pattern.test(uri));

    if (!isPatternMatch) {
      console.log('Redirect URI does not match allowed patterns:', uri);
      return false;
    }

    // Additional security checks

    // Block suspicious domains
    const blockedDomains = [
      'bit.ly',
      'tinyurl.com',
      'goo.gl',
      't.co',
      // Add other suspicious domains
    ];

    const isDomainBlocked = blockedDomains.some(domain =>
      url.hostname.includes(domain)
    );

    if (isDomainBlocked) {
      console.log('Redirect URI contains blocked domain:', uri);
      return false;
    }

    // Block non-HTTPS for production domains (except localhost and custom schemes)
    if (url.protocol === 'http:' &&
        !url.hostname.includes('localhost') &&
        !url.hostname.includes('127.0.0.1') &&
        !url.hostname.startsWith('192.168.')) {
      console.log('Non-HTTPS redirect URI not allowed for production:', uri);
      return false;
    }

    return true;
  } catch (error) {
    console.log('Invalid redirect URI format:', uri, error);
    return false;
  }
}
