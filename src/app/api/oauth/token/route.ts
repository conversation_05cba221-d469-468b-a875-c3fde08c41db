import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/db';
import { oauthClients, oauthAuthorizationCodes, oauthTokens, user } from '@/lib/db/schema';
import { eq, and, gt } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { sign } from 'jsonwebtoken';
import crypto from 'crypto';

/**
 * OAuth Token Endpoint
 * Exchanges authorization code for access token
 * 
 * Flow:
 * 1. Generated app sends authorization code + client credentials
 * 2. Verify code and client
 * 3. Generate JWT access token with app-scoped claims
 * 4. Return token to app
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      grant_type,
      code,
      client_id,
      client_secret,
      redirect_uri,
      code_verifier
    } = body;

    console.log('OAuth token request:', {
      grant_type,
      code: code?.slice(0, 8) + '...',
      client_id,
      redirect_uri,
      has_client_secret: !!client_secret
    });

    // Validate grant type
    if (grant_type !== 'authorization_code') {
      return NextResponse.json(
        { error: 'unsupported_grant_type' },
        { status: 400 }
      );
    }

    // Validate required parameters
    if (!code || !client_id || !redirect_uri) {
      return NextResponse.json(
        { error: 'invalid_request', error_description: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Verify client credentials
    console.log('Looking up client with ID:', client_id);

    const [client] = await db
      .select()
      .from(oauthClients)
      .where(eq(oauthClients.clientId, client_id))
      .limit(1);

    console.log('Found client:', client ? 'YES' : 'NO');

    if (!client) {
      console.error('Client not found for client_id:', client_id);
      return NextResponse.json(
        { error: 'invalid_client', error_description: 'Client not found' },
        { status: 401 }
      );
    }

    // For public clients (PKCE), we don't require client_secret
    // if (client.clientSecret && client.clientSecret !== client_secret) {
    //   return NextResponse.json(
    //     { error: 'invalid_client' },
    //     { status: 401 }
    //   );
    // }

    // Verify authorization code
    const [authCode] = await db
      .select()
      .from(oauthAuthorizationCodes)
      .where(
        and(
          eq(oauthAuthorizationCodes.code, code),
          eq(oauthAuthorizationCodes.clientId, client_id),
          eq(oauthAuthorizationCodes.redirectUri, redirect_uri),
          gt(oauthAuthorizationCodes.expiresAt, new Date())
        )
      )
      .limit(1);

    if (!authCode) {
      return NextResponse.json(
        { error: 'invalid_grant', error_description: 'Invalid or expired authorization code' },
        { status: 400 }
      );
    }

    // Verify PKCE if used
    // if (authCode.codeChallenge && authCode.codeChallengeMethod) {
    //   if (!code_verifier) {
    //     return NextResponse.json(
    //       { error: 'invalid_request', error_description: 'code_verifier required' },
    //       { status: 400 }
    //     );
    //   }
    //
    //   const hash = crypto.createHash('sha256').update(code_verifier).digest('base64url');
    //   if (hash !== authCode.codeChallenge) {
    //     return NextResponse.json(
    //       { error: 'invalid_grant', error_description: 'Invalid code_verifier' },
    //       { status: 400 }
    //     );
    //   }
    // }

    // Get user information for JWT
    const [userData] = await db
      .select({
        id: user.id,
        email: user.email,
        name: user.name,
        firstName: user.firstName,
        lastName: user.lastName,
      })
      .from(user)
      .where(eq(user.id, authCode.userId))
      .limit(1);

    if (!userData) {
      return NextResponse.json(
        { error: 'invalid_grant', error_description: 'User not found' },
        { status: 400 }
      );
    }

    // Generate tokens
    const accessToken = nanoid(32);
    const refreshToken = nanoid(32);
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    // Create JWT with app-scoped claims and user information
    const jwtPayload = {
      sub: authCode.userId,
      aud: client_id,
      app_id: client.appId,
      iss: process.env.NEXT_PUBLIC_APP_URL,
      exp: Math.floor(expiresAt.getTime() / 1000),
      iat: Math.floor(Date.now() / 1000),
      // User information for the app
      email: userData.email,
      name: userData.name || `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || userData.email,
      given_name: userData.firstName,
      family_name: userData.lastName,
    };

    const jwt = sign(jwtPayload, process.env.JWT_SECRET || 'fallback-secret');

    // Store token in database
    await db.insert(oauthTokens).values({
      accessToken,
      refreshToken,
      clientId: client_id,
      userId: authCode.userId,
      expiresAt,
      scope: 'read write',
    });

    // Delete used authorization code
    await db
      .delete(oauthAuthorizationCodes)
      .where(eq(oauthAuthorizationCodes.code, code));

    return NextResponse.json({
      access_token: jwt,
      token_type: 'Bearer',
      expires_in: 3600,
      refresh_token: refreshToken,
      scope: 'read write',
    });

  } catch (error) {
    console.error('OAuth token error:', error);
    return NextResponse.json(
      { error: 'server_error', error_description: 'Internal server error' },
      { status: 500 }
    );
  }
}
