import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { upsertInstantDbPlatformConfig } from '@/lib/db/instantdb-queries';

/**
 * POST /api/admin/instantdb/configure
 * Configure InstantDB Platform OAuth credentials
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin access
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // TODO: Add admin role check
    // For now, assuming all authenticated users can access admin endpoints
    // You should implement proper admin role checking here

    const body = await request.json();
    const { clientId, clientSecret } = body;

    if (!clientId || !clientSecret) {
      return NextResponse.json(
        { error: 'Client ID and Client Secret are required' },
        { status: 400 }
      );
    }

    // Validate the credentials by attempting to get an access token
    // This is where you would integrate with InstantDB Platform OAuth
    // For now, we'll just save the credentials
    
    // TODO: Implement actual OAuth flow with InstantDB
    // const tokenResponse = await fetch('https://api.instantdb.com/oauth/token', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/x-www-form-urlencoded',
    //   },
    //   body: new URLSearchParams({
    //     grant_type: 'client_credentials',
    //     client_id: clientId,
    //     client_secret: clientSecret,
    //   }),
    // });

    // if (!tokenResponse.ok) {
    //   const error = await tokenResponse.text();
    //   return NextResponse.json(
    //     { error: 'Invalid credentials: ' + error },
    //     { status: 400 }
    //   );
    // }

    // const tokenData = await tokenResponse.json();

    // For now, save without tokens (will be obtained later)
    const config = await upsertInstantDbPlatformConfig({
      clientId,
      clientSecret,
      // accessToken: tokenData.access_token,
      // refreshToken: tokenData.refresh_token,
      // tokenExpiresAt: new Date(Date.now() + tokenData.expires_in * 1000),
    });

    return NextResponse.json({
      message: 'Configuration saved successfully',
      configId: config.id,
    });
  } catch (error) {
    console.error('Failed to configure InstantDB platform:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
