import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getInstantDbPlatformConfig } from '@/lib/db/instantdb-queries';

export async function POST() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const config = await getInstantDbPlatformConfig();
    if (!config) {
      return NextResponse.json({ error: 'No configuration found' }, { status: 400 });
    }

    // Test the connection by making a simple API call
    if (!config.accessToken) {
      return NextResponse.json({ error: 'No access token available' }, { status: 400 });
    }

    try {
      const testResponse = await fetch('https://api.instantdb.com/superadmin/apps', {
        headers: {
          'Authorization': `Bearer ${config.accessToken}`,
        },
      });

      if (!testResponse.ok) {
        return NextResponse.json({ error: 'Access token is invalid or expired' }, { status: 400 });
      }

      return NextResponse.json({ message: 'Configuration reloaded successfully' });
    } catch (error) {
      return NextResponse.json({ error: 'Failed to test connection' }, { status: 500 });
    }
  } catch (error) {
    console.error('Failed to reload InstantDB configuration:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
