import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getInstantDbPlatformConfig } from '@/lib/db/instantdb-queries';

export async function POST() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const config = await getInstantDbPlatformConfig();
    if (!config) {
      return NextResponse.json({ error: 'No configuration found' }, { status: 400 });
    }

    // Test the connection by making a simple API call
    if (!config.accessToken) {
      return NextResponse.json({ error: 'No access token available' }, { status: 400 });
    }

    try {
      const testResponse = await fetch('https://api.instantdb.com/superadmin/apps', {
        headers: {
          'Authorization': `Bearer ${config.accessToken}`,
        },
        signal: AbortSignal.timeout(10000), // 10 second timeout
      });

      if (!testResponse.ok) {
        if (testResponse.status >= 500) {
          return NextResponse.json({ error: 'InstantDB service is currently unavailable' }, { status: 503 });
        }
        return NextResponse.json({ error: 'Access token is invalid or expired' }, { status: 400 });
      }

      return NextResponse.json({ message: 'Configuration reloaded successfully' });
    } catch (error) {
      if (error instanceof Error && (error.name === 'TimeoutError' || error.message.includes('timeout'))) {
        return NextResponse.json({ error: 'InstantDB service is not responding' }, { status: 503 });
      }
      return NextResponse.json({ error: 'Failed to connect to InstantDB service' }, { status: 500 });
    }
  } catch (error) {
    console.error('Failed to reload InstantDB configuration:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
