import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { upsertInstantDbPlatformConfig } from '@/lib/db/instantdb-queries';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { clientId, clientSecret } = body;

    if (!clientId || !clientSecret) {
      return NextResponse.json(
        { error: 'Client ID and Client Secret are required' },
        { status: 400 }
      );
    }

    await upsertInstantDbPlatformConfig({
      clientId,
      clientSecret,
    });

    return NextResponse.json({ message: 'Configuration saved successfully' });
  } catch (error) {
    console.error('Failed to setup InstantDB platform:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
