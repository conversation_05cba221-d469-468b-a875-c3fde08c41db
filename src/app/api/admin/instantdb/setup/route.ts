import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { upsertInstantDbPlatformConfig } from '@/lib/db/instantdb-queries';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { code } = body;

    if (!code) {
      return NextResponse.json({ error: 'Authorization code is required' }, { status: 400 });
    }

    // Exchange code for tokens
    const tokenResponse = await fetch('https://api.instantdb.com/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        client_id: process.env.INSTANTDB_PLATFORM_CLIENT_ID!,
        client_secret: process.env.INSTANTDB_PLATFORM_CLIENT_SECRET!,
        redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/api/admin/instantdb/callback`,
      }),
    });

    if (!tokenResponse.ok) {
      const error = await tokenResponse.text();
      return NextResponse.json({ error: 'Token exchange failed: ' + error }, { status: 400 });
    }

    const tokenData = await tokenResponse.json();

    // Save tokens to database
    await upsertInstantDbPlatformConfig({
      clientId: process.env.INSTANTDB_PLATFORM_CLIENT_ID!,
      clientSecret: process.env.INSTANTDB_PLATFORM_CLIENT_SECRET!,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token,
      tokenExpiresAt: new Date(Date.now() + tokenData.expires_in * 1000),
    });

    return NextResponse.json({ message: 'Authorization successful' });
  } catch (error) {
    console.error('Failed to complete OAuth flow:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
