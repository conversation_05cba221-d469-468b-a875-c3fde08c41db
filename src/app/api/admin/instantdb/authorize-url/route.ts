import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const clientId = process.env.INSTANTDB_PLATFORM_CLIENT_ID;
    const redirectUri = `${process.env.NEXT_PUBLIC_APP_URL}/api/admin/instantdb/callback`;

    if (!clientId) {
      return NextResponse.json({ error: 'InstantDB Platform Client ID not configured' }, { status: 500 });
    }

    // Generate CSRF state token
    const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

    const authUrl = new URL('https://api.instantdb.com/platform/oauth/start');
    authUrl.searchParams.set('client_id', clientId);
    authUrl.searchParams.set('redirect_uri', redirectUri);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('scope', 'apps-read apps-write');
    authUrl.searchParams.set('state', state);

    // TODO: Store state in session/database for CSRF verification
    // For now, we'll skip state verification (not recommended for production)

    return NextResponse.json({ authUrl: authUrl.toString(), state });
  } catch (error) {
    console.error('Failed to generate authorization URL:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
