import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const clientId = process.env.INSTANTDB_PLATFORM_CLIENT_ID;
    const redirectUri = `${process.env.NEXT_PUBLIC_APP_URL}/api/admin/instantdb/callback`;

    if (!clientId) {
      return NextResponse.json({ error: 'InstantDB Platform Client ID not configured' }, { status: 500 });
    }

    const authUrl = new URL('https://api.instantdb.com/oauth/authorize');
    authUrl.searchParams.set('client_id', clientId);
    authUrl.searchParams.set('redirect_uri', redirectUri);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('scope', 'apps-write');

    return NextResponse.json({ authUrl: authUrl.toString() });
  } catch (error) {
    console.error('Failed to generate authorization URL:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
