import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getAllInstantDbApps } from '@/lib/db/instantdb-queries';

/**
 * GET /api/admin/instantdb/apps
 * Get all provisioned InstantDB apps
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin access
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // TODO: Add admin role check
    // For now, assuming all authenticated users can access admin endpoints
    // You should implement proper admin role checking here

    const apps = await getAllInstantDbApps();
    
    // Sanitize sensitive data
    const sanitizedApps = apps.map(app => ({
      id: app.id,
      projectId: app.projectId,
      instantDbAppId: app.instantDbAppId,
      appName: app.appName,
      status: app.status,
      lastSyncedAt: app.lastSyncedAt,
      createdAt: app.createdAt,
      updatedAt: app.updatedAt,
      // Don't expose tokens
    }));

    return NextResponse.json(sanitizedApps);
  } catch (error) {
    console.error('Failed to get InstantDB apps:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
