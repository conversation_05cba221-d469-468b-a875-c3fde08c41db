import { NextRequest, NextResponse } from 'next/server';
import { upsertInstantDbPlatformConfig } from '@/lib/db/instantdb-queries';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const error = searchParams.get('error');

    if (error) {
      console.error('OAuth error:', error);
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/admin/instantdb?error=${encodeURIComponent(error)}`);
    }

    if (!code) {
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/admin/instantdb?error=no_code`);
    }

    // Exchange code for tokens
    const tokenResponse = await fetch('https://api.instantdb.com/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        client_id: process.env.INSTANTDB_PLATFORM_CLIENT_ID!,
        client_secret: process.env.INSTANTDB_PLATFORM_CLIENT_SECRET!,
        redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/api/admin/instantdb/callback`,
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Token exchange failed:', errorText);
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/admin/instantdb?error=token_exchange_failed`);
    }

    const tokenData = await tokenResponse.json();

    // Save tokens to database
    await upsertInstantDbPlatformConfig({
      clientId: process.env.INSTANTDB_PLATFORM_CLIENT_ID!,
      clientSecret: process.env.INSTANTDB_PLATFORM_CLIENT_SECRET!,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token,
      tokenExpiresAt: new Date(Date.now() + tokenData.expires_in * 1000),
    });

    // Redirect back to admin page with success
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/admin/instantdb?success=authorized`);
  } catch (error) {
    console.error('OAuth callback error:', error);
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/admin/instantdb?error=callback_failed`);
  }
}
