import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getInstantDbPlatformConfig } from '@/lib/db/instantdb-queries';

/**
 * GET /api/admin/instantdb/config
 * Get current InstantDB platform configuration
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin access
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // TODO: Add admin role check
    // For now, assuming all authenticated users can access admin endpoints
    // You should implement proper admin role checking here

    const config = await getInstantDbPlatformConfig();
    
    if (!config) {
      return NextResponse.json(null);
    }

    // Don't expose sensitive data in the response
    const sanitizedConfig = {
      id: config.id,
      clientId: config.clientId,
      clientSecret: config.clientSecret.replace(/./g, '*'), // Mask the secret
      accessToken: config.accessToken ? '***' : null,
      refreshToken: config.refreshToken ? '***' : null,
      tokenExpiresAt: config.tokenExpiresAt,
      isActive: config.isActive,
      lastRefreshedAt: config.lastRefreshedAt,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt,
    };

    return NextResponse.json(sanitizedConfig);
  } catch (error) {
    console.error('Failed to get InstantDB platform config:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
