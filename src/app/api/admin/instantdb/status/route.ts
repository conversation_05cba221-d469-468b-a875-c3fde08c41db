import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { isInstantDbPlatformConfigured } from '@/lib/db/instantdb-queries';

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const configured = await isInstantDbPlatformConfigured();
    return NextResponse.json({ configured });
  } catch (error) {
    console.error('Failed to check InstantDB status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
