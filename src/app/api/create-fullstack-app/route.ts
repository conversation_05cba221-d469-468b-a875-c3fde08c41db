import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { ConvexIntegrationProvider } from '@/lib/integrations/convex/ConvexIntegrationProvider';
import { generateConvexApp } from '@/lib/generators/convex-app-generator';
import { generateProject } from '@/app/(app)/projects/creating/actions';
import { saveFileStateAndCacheIfNeeded } from '@/lib/db/queries';
import { DEFAULT_CODE, DEFAULT_DEPENDENCIES } from '@/types/editor';
import { generateUUID } from '@/lib/utils';

/**
 * Create Full-Stack App Endpoint
 * This is the "Shopify for mobile apps" magic endpoint
 * 
 * Flow:
 * 1. User provides app idea
 * 2. Create Convex project
 * 3. Generate schema and functions
 * 4. Deploy to Convex
 * 5. Generate React Native app code
 * 6. Return working full-stack app
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { appIdea, appName } = body;

    if (!appIdea || !appName) {
      return NextResponse.json(
        { error: 'Missing required fields: appIdea, appName' },
        { status: 400 }
      );
    }

    console.log(`Creating full-stack app for user ${session.user.id}: ${appName}`);

    // Step 1: Create project using existing generateProject function
    const { project, chat, message } = await generateProject(
      appIdea,
      session.user.id,
      false, // not anonymous
      [], // no attachments
      'app' // chat type
    );

    // Step 2: Create Convex project
    const convexProvider = new ConvexIntegrationProvider();
    const convexProject = await convexProvider.createProject({
      userId: session.user.id,
      appName: project.appName || appName,
      projectId: project.id,
    });

    console.log('Convex project created:', convexProject.name);

    // Step 3: Generate schema based on app idea
    const schema = generateSchemaFromIdea(appIdea);

    // Step 4: Generate functions
    const functions = generateBasicFunctions(schema);

    // Step 5: Deploy to Convex (placeholder for now)
    await convexProvider.deployToProject({
      projectId: project.id,
      schema,
      functions,
    });

    console.log('Schema and functions deployed to Convex');

    // Step 6: Get OAuth configuration
    const oauthConfig = await convexProvider.getOAuthConfig(project.id);

    // Step 7: Generate React Native app code with TypeScript
    const convexAppFiles = generateConvexApp({
      appName: project.appName || appName,
      convexUrl: convexProject.deploymentUrl,
      oauthConfig,
      schema,
    });

    // Step 8: Merge with default template files
    const defaultFiles = [...DEFAULT_CODE];
    const mergedFiles = mergeConvexFilesWithDefault(defaultFiles, convexAppFiles);

    // Step 9: Save the initial file state using the existing system
    const userMessageId = message[0]?.id || generateUUID();
    await saveFileStateAndCacheIfNeeded({
      chatId: chat.id,
      messageId: userMessageId,
      files: mergedFiles,
      dependencies: {
        ...DEFAULT_DEPENDENCIES,
        'convex': { version: '^1.5.0' },
        'expo-web-browser': { version: '~12.3.2' },
        'expo-auth-session': { version: '~5.0.2' },
        '@types/react': { version: '~18.2.14' },
        '@types/react-native': { version: '~0.72.2' },
        'typescript': { version: '^5.1.3' }
      },
      shouldCache: true // Mark as base cache version
    });

    console.log('React Native app code generated and saved');

    // Step 10: Return everything
    return NextResponse.json({
      success: true,
      project: {
        id: project.id,
        name: project.appName || appName,
        slug: project.slug,
      },
      chat: {
        id: chat.id,
        title: chat.title,
      },
      convexProject: {
        name: convexProject.name,
        url: convexProject.deploymentUrl,
      },
      oauthConfig,
      schema,
      appCode: convexAppFiles.reduce((acc, file) => {
        acc[file.name] = file.content;
        return acc;
      }, {} as Record<string, string>),
      message: `🎉 Your full-stack app "${project.appName || appName}" is ready! Complete with authentication, database, and real-time features.`,
      instructions: [
        '1. Your app has been created and saved to your project',
        '2. Visit the project page to see the full code',
        '3. Download the code and run "yarn install"',
        '4. Run "expo start" to launch your app',
        '5. Your app includes working authentication via Magically.life',
        '6. All data is stored in your dedicated Convex database',
      ],
    });

  } catch (error) {
    console.error('Failed to create full-stack app:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create full-stack app',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Generate schema based on app idea using simple keyword matching
 */
function generateSchemaFromIdea(appIdea: string): Record<string, any> {
  const idea = appIdea.toLowerCase();
  
  const baseSchema = {
    users: {
      name: 'string',
      email: 'string',
      createdAt: 'number',
    },
  };

  // Todo/Task app
  if (idea.includes('todo') || idea.includes('task') || idea.includes('checklist')) {
    baseSchema['tasks'] = {
      title: 'string',
      description: 'string',
      completed: 'boolean',
      userId: 'string',
      dueDate: 'number',
      priority: 'string',
      createdAt: 'number',
    };
  }

  // Chat/Messaging app
  if (idea.includes('chat') || idea.includes('message') || idea.includes('messenger')) {
    baseSchema['messages'] = {
      content: 'string',
      userId: 'string',
      channelId: 'string',
      createdAt: 'number',
    };
    baseSchema['channels'] = {
      name: 'string',
      description: 'string',
      isPrivate: 'boolean',
      createdAt: 'number',
    };
  }

  // E-commerce/Shopping app
  if (idea.includes('shop') || idea.includes('store') || idea.includes('ecommerce') || idea.includes('marketplace')) {
    baseSchema['products'] = {
      name: 'string',
      description: 'string',
      price: 'number',
      imageUrl: 'string',
      category: 'string',
      inStock: 'boolean',
      createdAt: 'number',
    };
    baseSchema['orders'] = {
      userId: 'string',
      productIds: 'array',
      total: 'number',
      status: 'string',
      shippingAddress: 'string',
      createdAt: 'number',
    };
    baseSchema['cart'] = {
      userId: 'string',
      productId: 'string',
      quantity: 'number',
      createdAt: 'number',
    };
  }

  // Social media app
  if (idea.includes('social') || idea.includes('post') || idea.includes('feed') || idea.includes('instagram') || idea.includes('twitter')) {
    baseSchema['posts'] = {
      content: 'string',
      imageUrl: 'string',
      userId: 'string',
      likes: 'number',
      comments: 'number',
      createdAt: 'number',
    };
    baseSchema['follows'] = {
      followerId: 'string',
      followingId: 'string',
      createdAt: 'number',
    };
    baseSchema['likes'] = {
      userId: 'string',
      postId: 'string',
      createdAt: 'number',
    };
  }

  // Note-taking app
  if (idea.includes('note') || idea.includes('journal') || idea.includes('diary')) {
    baseSchema['notes'] = {
      title: 'string',
      content: 'string',
      userId: 'string',
      tags: 'array',
      isPrivate: 'boolean',
      createdAt: 'number',
      updatedAt: 'number',
    };
  }

  // Event/Calendar app
  if (idea.includes('event') || idea.includes('calendar') || idea.includes('schedule')) {
    baseSchema['events'] = {
      title: 'string',
      description: 'string',
      startTime: 'number',
      endTime: 'number',
      location: 'string',
      userId: 'string',
      attendees: 'array',
      createdAt: 'number',
    };
  }

  // Fitness/Health app
  if (idea.includes('fitness') || idea.includes('workout') || idea.includes('health') || idea.includes('exercise')) {
    baseSchema['workouts'] = {
      name: 'string',
      exercises: 'array',
      duration: 'number',
      calories: 'number',
      userId: 'string',
      createdAt: 'number',
    };
    baseSchema['exercises'] = {
      name: 'string',
      sets: 'number',
      reps: 'number',
      weight: 'number',
      workoutId: 'string',
      createdAt: 'number',
    };
  }

  return baseSchema;
}

/**
 * Generate basic CRUD functions for each table in the schema
 */
function generateBasicFunctions(schema: Record<string, any>): Record<string, string> {
  const functions: Record<string, string> = {};

  Object.keys(schema).forEach(tableName => {
    const fields = schema[tableName];
    
    // Create function
    functions[`create${capitalize(tableName)}`] = generateCreateFunction(tableName, fields);
    
    // List function
    functions[`list${capitalize(tableName)}`] = generateListFunction(tableName);
    
    // Get by ID function
    functions[`get${capitalize(tableName)}`] = generateGetFunction(tableName);
    
    // Update function
    functions[`update${capitalize(tableName)}`] = generateUpdateFunction(tableName, fields);
    
    // Delete function
    functions[`delete${capitalize(tableName)}`] = generateDeleteFunction(tableName);
  });

  return functions;
}

function generateCreateFunction(tableName: string, fields: Record<string, string>): string {
  return `import { mutation } from "./_generated/server";
import { v } from "convex/values";

export const create${capitalize(tableName)} = mutation({
  args: {
    ${Object.entries(fields).map(([key, type]) => 
      `${key}: v.${getConvexType(type)}`
    ).join(',\n    ')}
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("${tableName}", args);
  },
});`;
}

function generateListFunction(tableName: string): string {
  return `import { query } from "./_generated/server";

export const list${capitalize(tableName)} = query({
  handler: async (ctx) => {
    return await ctx.db.query("${tableName}").order("desc").collect();
  },
});`;
}

function generateGetFunction(tableName: string): string {
  return `import { query } from "./_generated/server";
import { v } from "convex/values";

export const get${capitalize(tableName)} = query({
  args: { id: v.id("${tableName}") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});`;
}

function generateUpdateFunction(tableName: string, fields: Record<string, string>): string {
  return `import { mutation } from "./_generated/server";
import { v } from "convex/values";

export const update${capitalize(tableName)} = mutation({
  args: {
    id: v.id("${tableName}"),
    ${Object.entries(fields).map(([key, type]) => 
      `${key}: v.optional(v.${getConvexType(type)})`
    ).join(',\n    ')}
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    return await ctx.db.patch(id, updates);
  },
});`;
}

function generateDeleteFunction(tableName: string): string {
  return `import { mutation } from "./_generated/server";
import { v } from "convex/values";

export const delete${capitalize(tableName)} = mutation({
  args: { id: v.id("${tableName}") },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.id);
  },
});`;
}

function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

function getConvexType(type: string): string {
  switch (type) {
    case 'string': return 'string()';
    case 'number': return 'number()';
    case 'boolean': return 'boolean()';
    case 'array': return 'array(v.any())';
    default: return 'string()';
  }
}

/**
 * Merge Convex-generated files with default template files
 * Convex files take precedence over default files with same names
 */
function mergeConvexFilesWithDefault(
  defaultFiles: Array<{name: string, content: string, language: string}>,
  convexFiles: Array<{name: string, content: string, language: string}>
): Array<{name: string, content: string, language: string}> {
  const mergedFiles = [...defaultFiles];

  // Add or replace files from Convex generation
  convexFiles.forEach(convexFile => {
    const existingIndex = mergedFiles.findIndex(file => file.name === convexFile.name);
    if (existingIndex >= 0) {
      // Replace existing file
      mergedFiles[existingIndex] = convexFile;
    } else {
      // Add new file
      mergedFiles.push(convexFile);
    }
  });

  return mergedFiles;
}
