import { NextRequest, NextResponse } from 'next/server';
import { MongoDBAtlasIntegrationProvider } from '@/lib/integrations/mongodb-atlas/MongoDBAtlasIntegrationProvider';
import { generateMongoDBAtlasApp } from '@/lib/generators/mongodb-atlas-app-generator';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { appName = 'TestApp', appIdea = 'A simple test app' } = body;

    // Test MongoDB Atlas Integration Provider
    const atlasProvider = new MongoDBAtlasIntegrationProvider();
    
    // Test credentials validation (will use env vars)
    const isValid = await atlasProvider.validateCredentials({
      credentials: {
        publicKey: process.env.MONGODB_ATLAS_PUBLIC_KEY || 'test',
        privateKey: process.env.MONGODB_ATLAS_PRIVATE_KEY || 'test',
        projectId: process.env.MONGODB_ATLAS_PROJECT_ID || 'test',
      }
    });

    // Generate test schema
    const schema = {
      tasks: {
        title: 'string',
        description: 'string',
        completed: 'boolean',
        userId: 'string',
        createdAt: 'date',
      },
      users: {
        name: 'string',
        email: 'string',
        createdAt: 'date',
      }
    };

    // Generate MongoDB Atlas app files
    const atlasFiles = generateMongoDBAtlasApp({
      appName,
      atlasAppId: 'test-atlas-app-id',
      oauthConfig: {
        clientId: 'test-client-id',
        authUrl: '/api/oauth/authorize',
        tokenUrl: '/api/oauth/token',
      },
      schema,
    });

    return NextResponse.json({
      success: true,
      message: 'MongoDB Atlas integration test completed',
      results: {
        credentialsValid: isValid,
        generatedFiles: atlasFiles.length,
        fileNames: atlasFiles.map(f => f.name),
        sampleFile: atlasFiles[0], // Return first file as sample
        schema,
      }
    });
  } catch (error) {
    console.error('MongoDB Atlas test error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'MongoDB Atlas integration test failed'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'MongoDB Atlas Integration Test Endpoint',
    usage: 'POST with { appName, appIdea } to test integration',
    environment: {
      hasPublicKey: !!process.env.MONGODB_ATLAS_PUBLIC_KEY,
      hasPrivateKey: !!process.env.MONGODB_ATLAS_PRIVATE_KEY,
      hasProjectId: !!process.env.MONGODB_ATLAS_PROJECT_ID,
    }
  });
}
