import { NextRequest, NextResponse } from 'next/server';
import { MongoDBAtlasIntegrationProvider } from '@/lib/integrations/mongodb-atlas/MongoDBAtlasIntegrationProvider';
import { generateMongoDBAtlasApp } from '@/lib/generators/mongodb-atlas-app-generator';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { appName = 'TestApp', appIdea = 'A simple test app' } = body;

    // Test MongoDB Atlas Integration Provider
    const atlasProvider = new MongoDBAtlasIntegrationProvider();
    
    // Test credentials validation (will use env vars)
    const isValid = await atlasProvider.validateCredentials({
      credentials: {
        publicKey: process.env.MONGODB_ATLAS_PUBLIC_KEY || 'test',
        privateKey: process.env.MONGODB_ATLAS_PRIVATE_KEY || 'test',
        projectId: process.env.MONGODB_ATLAS_PROJECT_ID || 'test',
      }
    });

    // Generate test schema
    const schema = {
      tasks: {
        title: 'string',
        description: 'string',
        completed: 'boolean',
        userId: 'string',
        createdAt: 'date',
      },
      users: {
        name: 'string',
        email: 'string',
        createdAt: 'date',
      }
    };

    // Generate test database name (database-per-app approach)
    const databaseName = `app_test_123_${appName.toLowerCase().replace(/[^a-z0-9]/g, '')}`;

    // Generate MongoDB Atlas app files
    const atlasFiles = generateMongoDBAtlasApp({
      appName,
      atlasAppId: 'test-atlas-app-id',
      databaseName,
      oauthConfig: {
        clientId: 'test-client-id',
        authUrl: '/api/oauth/authorize',
        tokenUrl: '/api/oauth/token',
      },
      schema,
    });

    return NextResponse.json({
      success: true,
      message: 'MongoDB Atlas integration test completed - Database-per-app approach',
      results: {
        credentialsValid: isValid,
        databaseName,
        architecture: 'database-per-app',
        generatedFiles: atlasFiles.length,
        fileNames: atlasFiles.map(f => f.name),
        sampleFile: atlasFiles[0], // Return first file as sample
        schema,
      }
    });
  } catch (error) {
    console.error('MongoDB Atlas test error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'MongoDB Atlas integration test failed'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'MongoDB Atlas Integration Test Endpoint - Database-Per-App Architecture',
    usage: 'POST with { appName, appIdea } to test integration',
    architecture: {
      approach: 'database-per-app',
      benefits: [
        '90% cost reduction vs project-per-app',
        'Perfect isolation with shared infrastructure',
        'Single Atlas project management',
        'Easier monitoring and debugging'
      ]
    },
    environment: {
      hasPublicKey: !!process.env.MONGODB_ATLAS_PUBLIC_KEY,
      hasPrivateKey: !!process.env.MONGODB_ATLAS_PRIVATE_KEY,
      hasProjectId: !!process.env.MONGODB_ATLAS_PROJECT_ID,
      hasClusterName: !!process.env.MONGODB_ATLAS_CLUSTER_NAME,
    }
  });
}
