import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { ConvexIntegrationProvider } from '@/lib/integrations/convex/ConvexIntegrationProvider';
import { db } from '@/lib/db/db';
import { projects } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

/**
 * Create a new Convex project for a user's app
 * This endpoint handles the "Shopify for mobile apps" flow
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { appName, appIdea, projectId } = body;

    if (!appName || !projectId) {
      return NextResponse.json(
        { error: 'Missing required fields: appName, projectId' },
        { status: 400 }
      );
    }

    // Verify user owns the project
    const [project] = await db
      .select()
      .from(projects)
      .where(eq(projects.id, projectId))
      .limit(1);

    if (!project || project.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      );
    }

    // Create Convex project
    const convexProvider = new ConvexIntegrationProvider();
    const convexProject = await convexProvider.createProject({
      userId: session.user.id,
      appName,
      projectId,
    });

    // Generate basic schema based on app idea
    const schema = generateSchemaFromIdea(appIdea || appName);
    
    // Generate basic functions
    const functions = generateBasicFunctions(schema);

    // Deploy to Convex
    await convexProvider.deployToProject({
      projectId,
      schema,
      functions,
    });

    // Get OAuth configuration for the app
    const oauthConfig = await convexProvider.getOAuthConfig(projectId);

    return NextResponse.json({
      success: true,
      convexProject,
      oauthConfig,
      schema,
      message: 'Convex project created successfully',
    });

  } catch (error) {
    console.error('Failed to create Convex project:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create Convex project',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Generate a basic schema based on app idea
 */
function generateSchemaFromIdea(appIdea: string): Record<string, any> {
  // Simple keyword-based schema generation
  const idea = appIdea.toLowerCase();
  
  const baseSchema = {
    users: {
      name: 'string',
      email: 'string',
      createdAt: 'number',
    },
  };

  // Add schema based on keywords
  if (idea.includes('todo') || idea.includes('task')) {
    baseSchema['tasks'] = {
      title: 'string',
      description: 'string',
      completed: 'boolean',
      userId: 'string',
      createdAt: 'number',
    };
  }

  if (idea.includes('chat') || idea.includes('message')) {
    baseSchema['messages'] = {
      content: 'string',
      userId: 'string',
      channelId: 'string',
      createdAt: 'number',
    };
    baseSchema['channels'] = {
      name: 'string',
      description: 'string',
      createdAt: 'number',
    };
  }

  if (idea.includes('shop') || idea.includes('store') || idea.includes('ecommerce')) {
    baseSchema['products'] = {
      name: 'string',
      description: 'string',
      price: 'number',
      imageUrl: 'string',
      createdAt: 'number',
    };
    baseSchema['orders'] = {
      userId: 'string',
      productIds: 'array',
      total: 'number',
      status: 'string',
      createdAt: 'number',
    };
  }

  if (idea.includes('social') || idea.includes('post')) {
    baseSchema['posts'] = {
      content: 'string',
      userId: 'string',
      likes: 'number',
      createdAt: 'number',
    };
    baseSchema['follows'] = {
      followerId: 'string',
      followingId: 'string',
      createdAt: 'number',
    };
  }

  return baseSchema;
}

/**
 * Generate basic CRUD functions for the schema
 */
function generateBasicFunctions(schema: Record<string, any>): Record<string, string> {
  const functions: Record<string, string> = {};

  // Generate functions for each table
  Object.keys(schema).forEach(tableName => {
    const fields = schema[tableName];
    
    // Create function
    functions[`create${capitalize(tableName)}`] = `
import { mutation } from "./_generated/server";
import { v } from "convex/values";

export const create${capitalize(tableName)} = mutation({
  args: {
    ${Object.entries(fields).map(([key, type]) => 
      `${key}: v.${getConvexType(type as string)}`
    ).join(',\n    ')}
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("${tableName}", args);
  },
});`;

    // List function
    functions[`list${capitalize(tableName)}`] = `
import { query } from "./_generated/server";

export const list${capitalize(tableName)} = query({
  handler: async (ctx) => {
    return await ctx.db.query("${tableName}").collect();
  },
});`;

    // Get by ID function
    functions[`get${capitalize(tableName)}`] = `
import { query } from "./_generated/server";
import { v } from "convex/values";

export const get${capitalize(tableName)} = query({
  args: { id: v.id("${tableName}") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});`;
  });

  return functions;
}

function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

function getConvexType(type: string): string {
  switch (type) {
    case 'string': return 'string()';
    case 'number': return 'number()';
    case 'boolean': return 'boolean()';
    case 'array': return 'array(v.any())';
    default: return 'string()';
  }
}
