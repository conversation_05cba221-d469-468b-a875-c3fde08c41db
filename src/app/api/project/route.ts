import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getProjectsByUserId } from '@/lib/db/queries';
import { generateProject } from '@/app/(app)/projects/creating/actions';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    const anonymousId = request.headers.get('x-anonymous-id');
    const userId = session?.user.id || anonymousId;

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const latest = searchParams.get('latest');

    // Get latest project with OAuth client info
    if (latest === 'true') {
      const projects = await getProjectsByUserId({ id: userId });
      const latestProject = projects[0]; // getProjectsByUserId returns ordered by updatedAt desc

      if (!latestProject) {
        return NextResponse.json({ project: null });
      }

      // Get OAuth client for this project
      const { db } = await import('@/lib/db/db');
      const { oauthClients } = await import('@/lib/db/schema');
      const { eq } = await import('drizzle-orm');

      const [oauthClient] = await db
        .select()
        .from(oauthClients)
        .where(eq(oauthClients.appId, latestProject.id))
        .limit(1);

      return NextResponse.json({
        project: {
          ...latestProject,
          oauthClientId: oauthClient?.clientId,
        }
      });
    }

    const projects = await getProjectsByUserId({ id: userId });

    return NextResponse.json(projects);
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const body = await request.json();
    const { message, backend = 'convex' } = body;

    // Use the full generateProject function which handles backend integration
    const { project, chat } = await generateProject(
      message,
      session.user.id,
      false, // not anonymous
      [], // no attachments
      'app', // chat type
      backend as 'convex' | 'mongodb-atlas'
    );

    return NextResponse.json({
      id: project.id,
      slug: project.slug,
      appName: project.appName
    });
  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}
