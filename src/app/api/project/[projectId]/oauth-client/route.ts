import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { db } from '@/lib/db/db';
import { projects, oauthClients } from '@/lib/db/schema';
import {desc, eq} from 'drizzle-orm';

/**
 * Get OAuth client information for a project
 * Used by the callback handler to get client_id for token exchange
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const { projectId } = await params;

    // Get project (no auth needed for OAuth client lookup)
    const [project] = await db
      .select()
      .from(projects)
      .where(eq(projects.id, projectId))
      .limit(1);

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Get OAuth client for this project
    const [oauthClient] = await db
      .select()
      .from(oauthClients)
      .where(eq(oauthClients.appId, projectId))
      .orderBy(desc(oauthClients.createdAt))
      .limit(1);

    console.log('oauthClient', oauthClient)
    if (!oauthClient) {
      // Create OAuth client if it doesn't exist (for legacy projects)
      const { nanoid } = await import('nanoid');

      const clientId = nanoid(32);
      const clientSecret = nanoid(64);

      const [newClient] = await db.insert(oauthClients).values({
        clientId,
        clientSecret,
        appId: projectId,
        redirectUri: `${process.env.NEXT_PUBLIC_APP_URL}/projects/${projectId}/callback/magically`,
        name: project.appName || 'Generated App',
      }).returning();

      return NextResponse.json({
        clientId: newClient.clientId,
        redirectUri: newClient.redirectUri,
      });
    }

    return NextResponse.json({
      clientId: oauthClient.clientId,
      redirectUri: oauthClient.redirectUri,
    });

  } catch (error) {
    console.error('Error fetching OAuth client:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
