'use server';

import {generateUUID, slugify} from "@/lib/utils";
import {generateProjectAttributes} from "@/app/(project)/projects/actions";
import {Attachment, CoreUserMessage, Message} from "ai";
import {createOrFetchAnonUser, getMessageCountForToday, saveChat, saveMessages, saveProject, saveFileStateAndCacheIfNeeded} from "@/lib/db/queries";
import {Message as DBMessage} from "@/lib/db/schema";
import {generateTitleFromUserMessage} from "@/app/(generator)/generator/actions";
import dayjs from "dayjs";
import {checkMessageLimit} from "@/lib/subscription";
import {DEFAULT_CODE, DEFAULT_DEPENDENCIES} from "@/types/editor";
import { createInstantDBClient, generateBasicSchema } from "@/lib/integrations/instantdb/InstantDBClient";
import { updateProjectInstantDB, updateProjectMongoDB } from "@/lib/db/project-queries";
import { MongoDBAtlasAdminClient } from "@/lib/integrations/mongodb-atlas/MongoDBAtlasAdminClient";
import { encrypt } from "@/lib/utils/encryption";

export async function generateProject(message: string, userId: string, isAnonymous: boolean, attachments: Attachment[] = [], chatType: 'app' | 'design' = 'app') {

    try {
        const userMessage: CoreUserMessage = {
            content: message,
            role: 'user',
            attachments: attachments
        } as CoreUserMessage;

        if (isAnonymous) {
            const anonUser = await createOrFetchAnonUser(userId);
            // const count = await getMessageCountForToday(userId)
            // if(count >= 3) {
            //     throw new Error("429: Message limit for the day reached");
            // }

            const messageCheck = await checkMessageLimit(userId!, isAnonymous);

            if(!messageCheck.canSendMessage) {
                throw new Error("429: Message limit for the day reached");
            }
        }

        // Generate project attributes using AI
        const [projectMetadata, title] = await Promise.all([
            generateProjectAttributes({
                messages: [userMessage] as Message[]
            }),
            generateTitleFromUserMessage({message: userMessage as CoreUserMessage})
        ])

        const projectId = generateUUID();
        const chatId = generateUUID();

        // Generate a unique slug based on the app name
        const baseSlug = slugify(projectMetadata.appName);
        const timestamp = Date.now().toString().slice(-6); // Use last 6 digits of timestamp for uniqueness
        const uniqueSlug = `${baseSlug}-${timestamp}`;

        // Save the project with the unique slug
        const project = await saveProject({
            id: projectId,
            userId: userId,
            slug: uniqueSlug,
            prompt: message,
            ...projectMetadata
        });

        const chat = await saveChat({
            id: chatId,
            userId,
            title,
            updatedAt: new Date(),
            projectId,
            isInitialized: false,
            type: chatType // Add the chat type parameter
        });
        const savedMessage = await saveMessages({
            messages: [{
                createdAt: dayjs().toDate(),
                userId: userId,
                projectId,
                chatId,
                role: userMessage.role,
                remoteProvider: null,
                remoteProviderId: null,
                // Format content as an array with text and image parts
                content: [
                    // First part is always the text message
                    {
                        type: "text",
                        text: userMessage.content
                    },
                    // Add image parts for each attachment
                    ...attachments.map(attachment => ({
                        type: "image",
                        mimeType: attachment.contentType || "image/png",
                        image: attachment.url
                    }))
                ]
            } as DBMessage]
        });

        // MongoDB Atlas Integration - Create app-specific database user
        try {
            console.log('Creating MongoDB Atlas user for:', projectMetadata.appName);

            // Create MongoDB Atlas admin client
            const atlasClient = new MongoDBAtlasAdminClient();

            // Create database user with scoped permissions
            const mongoUser = await atlasClient.createDatabaseUser(projectId);

            // Generate connection string
            const connectionString = atlasClient.generateConnectionString(mongoUser);

            // Encrypt password before storing
            const encryptedPassword = encrypt(mongoUser.password);

            // Update project with MongoDB credentials
            await updateProjectMongoDB(projectId, {
                mongodbUsername: mongoUser.username,
                mongodbPasswordEncrypted: encryptedPassword,
                mongodbDatabaseName: mongoUser.roles[0].db,
                mongodbConnectionString: connectionString,
            });

            // Generate MongoDB app files
            const mongoFiles = generateMongoDBApp({
                appName: projectMetadata.appName,
                projectId: projectId,
                databaseName: mongoUser.roles[0].db,
            });

            // Merge with default template files
            const mergedFiles = mergeMongoFilesWithDefault([...DEFAULT_CODE], mongoFiles);

            // Merge dependencies
            const mergedDependencies = {
                ...DEFAULT_DEPENDENCIES,
                '@magically/sdk': { version: '^1.0.0' },
                'expo-web-browser': { version: '~12.3.2' },
                'expo-auth-session': { version: '~5.0.2' }
            };

            // Save initial file state with MongoDB integration
            await saveFileStateAndCacheIfNeeded({
                chatId: chatId,
                messageId: savedMessage[0].id,
                files: mergedFiles,
                dependencies: mergedDependencies,
                shouldCache: true
            });

            console.log('✅ MongoDB Atlas user created and files saved for:', projectMetadata.appName);
        } catch (backendError) {
            console.error('❌ Failed to create MongoDB Atlas user:', backendError);

            // Check if it's a service availability issue
            const errorMessage = backendError instanceof Error ? backendError.message : String(backendError);
            const isServiceDown = errorMessage.includes('fetch') ||
                                 errorMessage.includes('network') ||
                                 errorMessage.includes('timeout') ||
                                 errorMessage.includes('503') ||
                                 errorMessage.includes('502') ||
                                 errorMessage.includes('500');

            if (isServiceDown) {
                // Service is down - fail the entire app creation
                throw new Error('Our backend services are temporarily unavailable. Please try again in a few minutes.');
            } else {
                // Other error - continue without backend but log the issue
                console.error('Non-service error during MongoDB creation:', backendError);
                await saveFileStateAndCacheIfNeeded({
                    chatId: chatId,
                    messageId: savedMessage[0].id,
                    files: DEFAULT_CODE,
                    dependencies: DEFAULT_DEPENDENCIES,
                    shouldCache: true
                });
            }
        }

        return {
            project,
            chat,
            message: savedMessage
        }
    } catch (e: any) {
        console.log('Error generating new project', e)
        throw e;
    }
}

/**
 * Determine app type from user message for schema generation
 */
function determineAppType(appIdea: string): string {
  const idea = appIdea.toLowerCase();

  if (idea.includes('todo') || idea.includes('task') || idea.includes('checklist')) {
    return 'todo';
  }
  if (idea.includes('chat') || idea.includes('message') || idea.includes('messenger')) {
    return 'chat';
  }
  if (idea.includes('note') || idea.includes('journal') || idea.includes('diary')) {
    return 'notes';
  }

  return 'generic'; // Default fallback
}

/**
 * Generate InstantDB app files (placeholder - to be implemented)
 */
function generateInstantDBApp(config: {
  appName: string;
  instantDbAppId: string;
  schema: any;
}): Array<{name: string, content: string, language: string}> {
  // TODO: Implement InstantDB app file generation
  // This should generate React Native files that use the Magically SDK
  return [
    {
      name: 'lib/instantdb.ts',
      content: `// InstantDB configuration
import { createMagicallyApp } from '@magically/sdk';

export const config = createMagicallyApp({
  appId: '${config.instantDbAppId}',
  instantDbAppId: '${config.instantDbAppId}',
  oauthClientId: '${config.instantDbAppId}',
});
`,
      language: 'typescript'
    }
  ];
}

/**
 * Merge InstantDB-generated files with default template files
 * InstantDB files take precedence over default files with same names
 */
function mergeInstantFilesWithDefault(
  defaultFiles: Array<{name: string, content: string, language: string}>,
  instantFiles: Array<{name: string, content: string, language: string}>
): Array<{name: string, content: string, language: string}> {
  const mergedFiles = [...defaultFiles];

  // Add or replace files from InstantDB generation
  instantFiles.forEach(instantFile => {
    const existingIndex = mergedFiles.findIndex(file => file.name === instantFile.name);
    if (existingIndex >= 0) {
      // Replace existing file
      mergedFiles[existingIndex] = instantFile;
    } else {
      // Add new file
      mergedFiles.push(instantFile);
    }
  });

  return mergedFiles;
}

/**
 * Generate MongoDB app files
 */
function generateMongoDBApp(config: {
  appName: string;
  projectId: string;
  databaseName: string;
}): Array<{name: string, content: string, language: string}> {
  return [
    {
      name: 'lib/mongodb.ts',
      content: `// MongoDB configuration
import { createMagicallyApp } from '@magically/sdk';

export const config = createMagicallyApp({
  appId: '${config.projectId}',
  databaseName: '${config.databaseName}',
  oauthClientId: 'magically-${config.projectId}',
});
`,
      language: 'typescript'
    },
    {
      name: 'magically/functions/api/index.ts',
      content: `// Auto-generated API function for ${config.appName}
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { MongoClient } from "https://deno.land/x/mongo@v0.32.0/mod.ts";

const APP_ID = Deno.env.get("APP_ID")!;
const MONGODB_URI = Deno.env.get("MONGODB_URI")!;

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      },
    });
  }

  let mongoClient: MongoClient | null = null;

  try {
    console.log('📊 API function called at:', new Date().toISOString());

    // Connect to MongoDB
    mongoClient = new MongoClient();
    await mongoClient.connect(MONGODB_URI);

    const db = mongoClient.database(APP_ID);
    const collection = db.collection('items');

    const url = new URL(req.url);
    const method = req.method;

    if (method === 'GET' && url.pathname === '/items') {
      const items = await collection.find({}).toArray();
      return new Response(JSON.stringify(items), {
        headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
      });
    }

    if (method === 'POST' && url.pathname === '/items') {
      const body = await req.json();
      const item = {
        ...body,
        id: crypto.randomUUID(),
        createdAt: new Date(),
      };

      await collection.insertOne(item);
      return new Response(JSON.stringify(item), {
        headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
      });
    }

    return new Response(JSON.stringify({ error: 'Not found' }), {
      status: 404,
      headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
    });

  } catch (error) {
    console.error('❌ API error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
    });
  } finally {
    if (mongoClient) {
      await mongoClient.close();
    }
  }
});
`,
      language: 'typescript'
    }
  ];
}

/**
 * Merge MongoDB-generated files with default template files
 */
function mergeMongoFilesWithDefault(
  defaultFiles: Array<{name: string, content: string, language: string}>,
  mongoFiles: Array<{name: string, content: string, language: string}>
): Array<{name: string, content: string, language: string}> {
  const mergedFiles = [...defaultFiles];

  // Add or replace files from MongoDB generation
  mongoFiles.forEach(mongoFile => {
    const existingIndex = mergedFiles.findIndex(file => file.name === mongoFile.name);
    if (existingIndex >= 0) {
      // Replace existing file
      mergedFiles[existingIndex] = mongoFile;
    } else {
      // Add new file
      mergedFiles.push(mongoFile);
    }
  });

  return mergedFiles;
}
