'use server';

import {generateUUID, slugify} from "@/lib/utils";
import {generateProjectAttributes} from "@/app/(project)/projects/actions";
import {Attachment, CoreUserMessage, Message} from "ai";
import {createOrFetchAnonUser, getMessageCountForToday, saveChat, saveMessages, saveProject, saveFileStateAndCacheIfNeeded} from "@/lib/db/queries";
import {Message as DBMessage} from "@/lib/db/schema";
import {generateTitleFromUserMessage} from "@/app/(generator)/generator/actions";
import dayjs from "dayjs";
import {checkMessageLimit} from "@/lib/subscription";
import {DEFAULT_CODE, DEFAULT_DEPENDENCIES} from "@/types/editor";
import { createInstantDBClient, generateBasicSchema } from "@/lib/integrations/instantdb/InstantDBClient";
import { updateProjectInstantDB } from "@/lib/db/project-queries";

export async function generateProject(message: string, userId: string, isAnonymous: boolean, attachments: Attachment[] = [], chatType: 'app' | 'design' = 'app') {

    try {
        const userMessage: CoreUserMessage = {
            content: message,
            role: 'user',
            attachments: attachments
        } as CoreUserMessage;

        if (isAnonymous) {
            const anonUser = await createOrFetchAnonUser(userId);
            // const count = await getMessageCountForToday(userId)
            // if(count >= 3) {
            //     throw new Error("429: Message limit for the day reached");
            // }

            const messageCheck = await checkMessageLimit(userId!, isAnonymous);

            if(!messageCheck.canSendMessage) {
                throw new Error("429: Message limit for the day reached");
            }
        }

        // Generate project attributes using AI
        const [projectMetadata, title] = await Promise.all([
            generateProjectAttributes({
                messages: [userMessage] as Message[]
            }),
            generateTitleFromUserMessage({message: userMessage as CoreUserMessage})
        ])

        const projectId = generateUUID();
        const chatId = generateUUID();

        // Generate a unique slug based on the app name
        const baseSlug = slugify(projectMetadata.appName);
        const timestamp = Date.now().toString().slice(-6); // Use last 6 digits of timestamp for uniqueness
        const uniqueSlug = `${baseSlug}-${timestamp}`;

        // Save the project with the unique slug
        const project = await saveProject({
            id: projectId,
            userId: userId,
            slug: uniqueSlug,
            prompt: message,
            ...projectMetadata
        });

        const chat = await saveChat({
            id: chatId,
            userId,
            title,
            updatedAt: new Date(),
            projectId,
            isInitialized: false,
            type: chatType // Add the chat type parameter
        });
        const savedMessage = await saveMessages({
            messages: [{
                createdAt: dayjs().toDate(),
                userId: userId,
                projectId,
                chatId,
                role: userMessage.role,
                remoteProvider: null,
                remoteProviderId: null,
                // Format content as an array with text and image parts
                content: [
                    // First part is always the text message
                    {
                        type: "text",
                        text: userMessage.content
                    },
                    // Add image parts for each attachment
                    ...attachments.map(attachment => ({
                        type: "image",
                        mimeType: attachment.contentType || "image/png",
                        image: attachment.url
                    }))
                ]
            } as DBMessage]
        });

        // InstantDB Integration - Create backend and generate initial files
        try {
            console.log('Creating InstantDB app for:', projectMetadata.appName);

            // Create InstantDB client
            const instantClient = await createInstantDBClient();

            // Create new InstantDB app
            const instantApp = await instantClient.createApp(projectMetadata.appName);

            // Update project with InstantDB information
            await updateProjectInstantDB(projectId, {
                instantDbAppId: instantApp.id,
                instantDbAppTitle: instantApp.title,
                instantDbCreatedAt: new Date(instantApp.created_at),
            });

            // Determine app type from message for schema generation
            const appType = determineAppType(message);

            // Generate and apply schema
            const schema = generateBasicSchema(appType);
            await instantClient.applySchemaPush(instantApp.id, schema);

            // Generate InstantDB app files (TODO: implement generator)
            const instantFiles = generateInstantDBApp({
                appName: projectMetadata.appName,
                instantDbAppId: instantApp.id,
                schema,
            });

            // Merge with default template files
            const mergedFiles = mergeInstantFilesWithDefault([...DEFAULT_CODE], instantFiles);

            // Merge dependencies
            const mergedDependencies = {
                ...DEFAULT_DEPENDENCIES,
                '@instantdb/react-native': { version: '^0.12.0' },
                '@magically/sdk': { version: '^1.0.0' },
                'expo-web-browser': { version: '~12.3.2' },
                'expo-auth-session': { version: '~5.0.2' }
            };

            // Save initial file state with InstantDB integration
            await saveFileStateAndCacheIfNeeded({
                chatId: chatId,
                messageId: savedMessage[0].id,
                files: mergedFiles,
                dependencies: mergedDependencies,
                shouldCache: true
            });

            console.log('✅ InstantDB app created and files saved for:', projectMetadata.appName);
        } catch (backendError) {
            console.error('❌ Failed to create InstantDB app:', backendError);

            // Check if it's a service availability issue
            const errorMessage = backendError instanceof Error ? backendError.message : String(backendError);
            const isServiceDown = errorMessage.includes('fetch') ||
                                 errorMessage.includes('network') ||
                                 errorMessage.includes('timeout') ||
                                 errorMessage.includes('503') ||
                                 errorMessage.includes('502') ||
                                 errorMessage.includes('500');

            if (isServiceDown) {
                // Service is down - fail the entire app creation
                throw new Error('Our backend services are temporarily unavailable. Please try again in a few minutes.');
            } else {
                // Other error - continue without backend but log the issue
                console.error('Non-service error during InstantDB creation:', backendError);
                await saveFileStateAndCacheIfNeeded({
                    chatId: chatId,
                    messageId: savedMessage[0].id,
                    files: DEFAULT_CODE,
                    dependencies: DEFAULT_DEPENDENCIES,
                    shouldCache: true
                });
            }
        }

        return {
            project,
            chat,
            message: savedMessage
        }
    } catch (e: any) {
        console.log('Error generating new project', e)
        throw e;
    }
}

/**
 * Determine app type from user message for schema generation
 */
function determineAppType(appIdea: string): string {
  const idea = appIdea.toLowerCase();

  if (idea.includes('todo') || idea.includes('task') || idea.includes('checklist')) {
    return 'todo';
  }
  if (idea.includes('chat') || idea.includes('message') || idea.includes('messenger')) {
    return 'chat';
  }
  if (idea.includes('note') || idea.includes('journal') || idea.includes('diary')) {
    return 'notes';
  }

  return 'generic'; // Default fallback
}

/**
 * Generate InstantDB app files (placeholder - to be implemented)
 */
function generateInstantDBApp(config: {
  appName: string;
  instantDbAppId: string;
  schema: any;
}): Array<{name: string, content: string, language: string}> {
  // TODO: Implement InstantDB app file generation
  // This should generate React Native files that use the Magically SDK
  return [
    {
      name: 'lib/instantdb.ts',
      content: `// InstantDB configuration
import { createMagicallyApp } from '@magically/sdk';

export const config = createMagicallyApp({
  appId: '${config.instantDbAppId}',
  instantDbAppId: '${config.instantDbAppId}',
  oauthClientId: '${config.instantDbAppId}',
});
`,
      language: 'typescript'
    }
  ];
}

/**
 * Merge InstantDB-generated files with default template files
 * InstantDB files take precedence over default files with same names
 */
function mergeInstantFilesWithDefault(
  defaultFiles: Array<{name: string, content: string, language: string}>,
  instantFiles: Array<{name: string, content: string, language: string}>
): Array<{name: string, content: string, language: string}> {
  const mergedFiles = [...defaultFiles];

  // Add or replace files from InstantDB generation
  instantFiles.forEach(instantFile => {
    const existingIndex = mergedFiles.findIndex(file => file.name === instantFile.name);
    if (existingIndex >= 0) {
      // Replace existing file
      mergedFiles[existingIndex] = instantFile;
    } else {
      // Add new file
      mergedFiles.push(instantFile);
    }
  });

  return mergedFiles;
}
