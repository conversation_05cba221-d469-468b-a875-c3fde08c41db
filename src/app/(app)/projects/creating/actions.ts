'use server';

import {generateUUID, slugify} from "@/lib/utils";
import {generateProjectAttributes} from "@/app/(project)/projects/actions";
import {Attachment, CoreUserMessage, Message} from "ai";
import {createOrFetchAnonUser, getMessageCountForToday, saveChat, saveMessages, saveProject, saveFileStateAndCacheIfNeeded} from "@/lib/db/queries";
import {Message as DBMessage} from "@/lib/db/schema";
import {generateTitleFromUserMessage} from "@/app/(generator)/generator/actions";
import dayjs from "dayjs";
import {checkMessageLimit} from "@/lib/subscription";
import {DEFAULT_CODE, DEFAULT_DEPENDENCIES} from "@/types/editor";
import {ConvexIntegrationProvider} from "@/lib/integrations/convex/ConvexIntegrationProvider";
import {generateConvexApp} from "@/lib/generators/convex-app-generator";
import {MongoDBAtlasIntegrationProvider} from "@/lib/integrations/mongodb-atlas/MongoDBAtlasIntegrationProvider";
import {generateMongoDBAtlasApp} from "@/lib/generators/mongodb-atlas-app-generator";

export async function generateProject(message: string, userId: string, isAnonymous: boolean, attachments: Attachment[] = [], chatType: 'app' | 'design' = 'app', backend: 'convex' | 'mongodb-atlas' = 'convex') {

    try {
        const userMessage: CoreUserMessage = {
            content: message,
            role: 'user',
            attachments: attachments
        } as CoreUserMessage;

        if (isAnonymous) {
            const anonUser = await createOrFetchAnonUser(userId);
            // const count = await getMessageCountForToday(userId)
            // if(count >= 3) {
            //     throw new Error("429: Message limit for the day reached");
            // }

            const messageCheck = await checkMessageLimit(userId!, isAnonymous);

            if(!messageCheck.canSendMessage) {
                throw new Error("429: Message limit for the day reached");
            }
        }

        // Generate project attributes using AI
        const [projectMetadata, title] = await Promise.all([
            generateProjectAttributes({
                messages: [userMessage] as Message[]
            }),
            generateTitleFromUserMessage({message: userMessage as CoreUserMessage})
        ])

        const projectId = generateUUID();
        const chatId = generateUUID();

        // Generate a unique slug based on the app name
        const baseSlug = slugify(projectMetadata.appName);
        const timestamp = Date.now().toString().slice(-6); // Use last 6 digits of timestamp for uniqueness
        const uniqueSlug = `${baseSlug}-${timestamp}`;

        // Save the project with the unique slug
        const project = await saveProject({
            id: projectId,
            userId: userId,
            slug: uniqueSlug,
            prompt: message,
            ...projectMetadata
        });

        const chat = await saveChat({
            id: chatId,
            userId,
            title,
            updatedAt: new Date(),
            projectId,
            isInitialized: false,
            type: chatType // Add the chat type parameter
        });
        const savedMessage = await saveMessages({
            messages: [{
                createdAt: dayjs().toDate(),
                userId: userId,
                projectId,
                chatId,
                role: userMessage.role,
                remoteProvider: null,
                remoteProviderId: null,
                // Format content as an array with text and image parts
                content: [
                    // First part is always the text message
                    {
                        type: "text",
                        text: userMessage.content
                    },
                    // Add image parts for each attachment
                    ...attachments.map(attachment => ({
                        type: "image",
                        mimeType: attachment.contentType || "image/png",
                        image: attachment.url
                    }))
                ]
            } as DBMessage]
        });

        // Backend Integration - Create backend and generate initial files
        try {
            if (backend === 'mongodb-atlas') {
                console.log('Creating MongoDB Atlas project for:', projectMetadata.appName);

                // Create MongoDB Atlas App Services project
                const atlasProvider = new MongoDBAtlasIntegrationProvider();
                const atlasProject = await atlasProvider.createProject({
                    userId: userId,
                    appName: projectMetadata.appName,
                    projectId: projectId,
                });

                // Generate schema based on app idea
                const schema = generateSchemaFromIdea(message);

                // Get OAuth configuration
                const oauthConfig = await atlasProvider.getOAuthConfig(projectId);

                // Generate MongoDB Atlas TypeScript files
                const atlasFiles = generateMongoDBAtlasApp({
                    appName: projectMetadata.appName,
                    atlasAppId: atlasProject.id,
                    oauthConfig,
                    schema,
                });

                // Merge with default template files
                const mergedFiles = mergeAtlasFilesWithDefault([...DEFAULT_CODE], atlasFiles);

                // Merge dependencies
                const mergedDependencies = {
                    ...DEFAULT_DEPENDENCIES,
                    'realm': { version: '^12.0.0' },
                    '@realm/react': { version: '^0.6.0' },
                    'expo-web-browser': { version: '~12.3.2' },
                    'expo-auth-session': { version: '~5.0.2' }
                };

                // Save initial file state with Atlas integration
                await saveFileStateAndCacheIfNeeded({
                    chatId: chatId,
                    messageId: savedMessage[0].id,
                    files: mergedFiles,
                    dependencies: mergedDependencies,
                    shouldCache: true
                });

                console.log('✅ MongoDB Atlas project created and files saved for:', projectMetadata.appName);
            } else {
                // Default to Convex
                console.log('Creating Convex project for:', projectMetadata.appName);

                // Create Convex project
                const convexProvider = new ConvexIntegrationProvider();
                const convexProject = await convexProvider.createProject({
                    userId: userId,
                    appName: projectMetadata.appName,
                    projectId: projectId,
                });

                // Generate schema based on app idea
                const schema = generateSchemaFromIdea(message);

                // Get OAuth configuration
                const oauthConfig = await convexProvider.getOAuthConfig(projectId);

                // Generate Convex TypeScript files
                const convexFiles = generateConvexApp({
                    appName: projectMetadata.appName,
                    convexUrl: convexProject.deploymentUrl,
                    oauthConfig,
                    schema,
                });

                // Merge with default template files
                const mergedFiles = mergeConvexFilesWithDefault([...DEFAULT_CODE], convexFiles);

                // Merge dependencies
                const mergedDependencies = {
                    ...DEFAULT_DEPENDENCIES,
                    'convex': { version: '^1.5.0' },
                    'expo-web-browser': { version: '~12.3.2' },
                    'expo-auth-session': { version: '~5.0.2' }
                };

                // Save initial file state with Convex integration
                await saveFileStateAndCacheIfNeeded({
                    chatId: chatId,
                    messageId: savedMessage[0].id,
                    files: mergedFiles,
                    dependencies: mergedDependencies,
                    shouldCache: true
                });

                console.log('✅ Convex project created and files saved for:', projectMetadata.appName);
            }
        } catch (backendError) {
            console.error(`❌ Failed to create ${backend} project:`, backendError);
            // Continue without backend integration - save default files
            await saveFileStateAndCacheIfNeeded({
                chatId: chatId,
                messageId: savedMessage[0].id,
                files: DEFAULT_CODE,
                dependencies: DEFAULT_DEPENDENCIES,
                shouldCache: true
            });
        }

        return {
            project,
            chat,
            message: savedMessage
        }
    } catch (e: any) {
        console.log('Error generating new project', e)
        throw e;
    }
}

/**
 * Generate schema based on app idea using simple keyword matching
 */
function generateSchemaFromIdea(appIdea: string): Record<string, any> {
  const idea = appIdea.toLowerCase();

  const baseSchema = {
    users: {
      name: 'string',
      email: 'string',
      createdAt: 'number',
    },
  };

  // Todo/Task app
  if (idea.includes('todo') || idea.includes('task') || idea.includes('checklist')) {
    baseSchema['tasks'] = {
      title: 'string',
      description: 'string',
      completed: 'boolean',
      userId: 'string',
      dueDate: 'number',
      priority: 'string',
      createdAt: 'number',
    };
  }

  // Chat/Messaging app
  if (idea.includes('chat') || idea.includes('message') || idea.includes('messenger')) {
    baseSchema['messages'] = {
      content: 'string',
      userId: 'string',
      channelId: 'string',
      createdAt: 'number',
    };
    baseSchema['channels'] = {
      name: 'string',
      description: 'string',
      isPrivate: 'boolean',
      createdAt: 'number',
    };
  }

  // E-commerce/Shopping app
  if (idea.includes('shop') || idea.includes('store') || idea.includes('ecommerce') || idea.includes('marketplace')) {
    baseSchema['products'] = {
      name: 'string',
      description: 'string',
      price: 'number',
      imageUrl: 'string',
      category: 'string',
      inStock: 'boolean',
      createdAt: 'number',
    };
    baseSchema['orders'] = {
      userId: 'string',
      productIds: 'array',
      total: 'number',
      status: 'string',
      shippingAddress: 'string',
      createdAt: 'number',
    };
  }

  // Social media app
  if (idea.includes('social') || idea.includes('post') || idea.includes('feed')) {
    baseSchema['posts'] = {
      content: 'string',
      imageUrl: 'string',
      userId: 'string',
      likes: 'number',
      comments: 'number',
      createdAt: 'number',
    };
    baseSchema['follows'] = {
      followerId: 'string',
      followingId: 'string',
      createdAt: 'number',
    };
  }

  // Note-taking app
  if (idea.includes('note') || idea.includes('journal') || idea.includes('diary')) {
    baseSchema['notes'] = {
      title: 'string',
      content: 'string',
      userId: 'string',
      tags: 'array',
      isPrivate: 'boolean',
      createdAt: 'number',
    };
  }

  // Fitness/Health app
  if (idea.includes('fitness') || idea.includes('workout') || idea.includes('health')) {
    baseSchema['workouts'] = {
      name: 'string',
      exercises: 'array',
      duration: 'number',
      calories: 'number',
      userId: 'string',
      createdAt: 'number',
    };
  }

  return baseSchema;
}

/**
 * Merge Convex-generated files with default template files
 * Convex files take precedence over default files with same names
 */
function mergeConvexFilesWithDefault(
  defaultFiles: Array<{name: string, content: string, language: string}>,
  convexFiles: Array<{name: string, content: string, language: string}>
): Array<{name: string, content: string, language: string}> {
  const mergedFiles = [...defaultFiles];

  // Add or replace files from Convex generation
  convexFiles.forEach(convexFile => {
    const existingIndex = mergedFiles.findIndex(file => file.name === convexFile.name);
    if (existingIndex >= 0) {
      // Replace existing file
      mergedFiles[existingIndex] = convexFile;
    } else {
      // Add new file
      mergedFiles.push(convexFile);
    }
  });

  return mergedFiles;
}

/**
 * Merge MongoDB Atlas-generated files with default template files
 * Atlas files take precedence over default files with same names
 */
function mergeAtlasFilesWithDefault(
  defaultFiles: Array<{name: string, content: string, language: string}>,
  atlasFiles: Array<{name: string, content: string, language: string}>
): Array<{name: string, content: string, language: string}> {
  const mergedFiles = [...defaultFiles];

  // Add or replace files from Atlas generation
  atlasFiles.forEach(atlasFile => {
    const existingIndex = mergedFiles.findIndex(file => file.name === atlasFile.name);
    if (existingIndex >= 0) {
      // Replace existing file
      mergedFiles[existingIndex] = atlasFile;
    } else {
      // Add new file
      mergedFiles.push(atlasFile);
    }
  });

  return mergedFiles;
}
