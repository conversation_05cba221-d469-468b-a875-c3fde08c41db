'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, CheckCircle, AlertCircle, Download, ExternalLink } from 'lucide-react';

export default function TestMongoDBOAuthPage() {
  const [appName, setAppName] = useState('');
  const [appIdea, setAppIdea] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingLatest, setLoadingLatest] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [latestApp, setLatestApp] = useState<any>(null);

  // Test data for quick testing
  const testApps = [
    {
      name: 'TaskMaster',
      idea: 'A todo app where users can create tasks, mark them as complete, set priorities, and organize by categories'
    },
    {
      name: 'ChatHub',
      idea: 'A messaging app where users can create channels, send messages, and have real-time conversations'
    },
    {
      name: 'ShopEasy',
      idea: 'An e-commerce app where users can browse products, add to cart, and place orders'
    },
    {
      name: 'FitTracker',
      idea: 'A fitness app where users can log workouts, track exercises, and monitor their progress'
    }
  ];

  const handleCreateApp = async () => {
    if (!appName.trim() || !appIdea.trim()) {
      setError('Please provide both app name and description');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Use the standard project creation flow with MongoDB Atlas
      const response = await fetch('/api/project', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: `Create a ${appName.trim()}: ${appIdea.trim()}`,
          backend: 'mongodb-atlas', // Specify MongoDB Atlas backend
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}: Failed to create project`);
      }

      // Format the response to match our expected structure
      setResult({
        success: true,
        project: {
          id: data.id,
          name: data.appName,
          slug: data.slug,
        },
        message: `🎉 Your full-stack app "${data.appName}" has been created! Complete with MongoDB Atlas backend, TypeScript, and authentication.`,
        instructions: [
          '1. Your project has been created with MongoDB Atlas integration',
          '2. TypeScript files with Realm SDK are ready in your project',
          '3. OAuth authentication is configured',
          '4. Real-time Device Sync is enabled',
          '5. Visit the project to continue development',
        ],
      });
    } catch (err) {
      console.error('Create app error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const loadTestApp = (testApp: typeof testApps[0]) => {
    setAppName(testApp.name);
    setAppIdea(testApp.idea);
    setError(null);
    setResult(null);
  };

  // Load latest app on component mount
  React.useEffect(() => {
    loadLatestApp();
  }, []);

  const loadLatestApp = async () => {
    try {
      setLoadingLatest(true);
      const response = await fetch('/api/project?latest=true&backend=mongodb-atlas');
      if (response.ok) {
        const data = await response.json();
        if (data.project) {
          setLatestApp(data.project);
          // If we have a latest app, load its details
          if (data.project.mongodbAtlasAppId) {
            setResult({
              project: data.project,
              atlasProject: {
                name: data.project.mongodbAtlasAppId,
                url: data.project.mongodbAtlasUrl,
              },
              oauthConfig: {
                clientId: data.project.oauthClientId || 'loading...',
                authUrl: '/api/oauth/authorize',
                tokenUrl: '/api/oauth/token',
              },
              schema: data.project.schema || {},
              message: `Loaded existing app: ${data.project.appName}`,
            });
          }
        }
      }
    } catch (err) {
      console.error('Failed to load latest app:', err);
    } finally {
      setLoadingLatest(false);
    }
  };

  const testOAuthEndpoints = async () => {
    try {
      if (!latestApp?.oauthClientId) {
        setError('No OAuth client found. Create an app first.');
        return;
      }

      // Use the actual OAuth client from the latest app
      const redirectUri = encodeURIComponent('exp://localhost:19000/--/auth');
      const authUrl = `/api/oauth/authorize?client_id=${latestApp.oauthClientId}&redirect_uri=${redirectUri}&response_type=code&state=test`;

      window.open(authUrl, '_blank');
    } catch (err) {
      setError('Failed to test OAuth endpoints');
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">MongoDB Atlas + OAuth Integration Test</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Test the "Shopify for mobile apps" experience. Create a full-stack React Native app with MongoDB Atlas backend using <strong>database-per-app</strong> architecture for perfect isolation and cost efficiency.
        </p>
      </div>

      <Tabs defaultValue="create" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="create">Create App</TabsTrigger>
          <TabsTrigger value="oauth">Test OAuth</TabsTrigger>
          <TabsTrigger value="docs">API Docs</TabsTrigger>
        </TabsList>

        <TabsContent value="create" className="space-y-6">
          {!result ? (
            <Card className="max-w-2xl mx-auto">
              <CardHeader>
                <CardTitle>Create Full-Stack App</CardTitle>
                <CardDescription>
                  Describe your app idea and get a complete React Native app with MongoDB Atlas backend
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-2">
                  <label className="text-sm font-medium">Quick Test Apps</label>
                  <div className="flex flex-wrap gap-2">
                    {testApps.map((testApp, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => loadTestApp(testApp)}
                        disabled={loading}
                      >
                        {testApp.name}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="appName" className="text-sm font-medium">App Name</label>
                  <Input
                    id="appName"
                    placeholder="e.g., TaskMaster, ChatApp, ShopEasy"
                    value={appName}
                    onChange={(e) => setAppName(e.target.value)}
                    disabled={loading}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="appIdea" className="text-sm font-medium">App Description</label>
                  <Textarea
                    id="appIdea"
                    placeholder="Describe your app idea in detail..."
                    value={appIdea}
                    onChange={(e) => setAppIdea(e.target.value)}
                    disabled={loading}
                    rows={4}
                  />
                </div>

                <Button 
                  onClick={handleCreateApp} 
                  disabled={loading || !appName.trim() || !appIdea.trim()}
                  className="w-full"
                  size="lg"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Your App... (This may take 30-60 seconds)
                    </>
                  ) : (
                    'Create Full-Stack App with MongoDB Atlas'
                  )}
                </Button>
              </CardContent>
            </Card>
          ) : (
            <Card className="max-w-4xl mx-auto">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-6 w-6 text-green-500" />
                  App Created Successfully!
                </CardTitle>
                <CardDescription>
                  Your full-stack app "{result.project.name}" is ready with MongoDB Atlas
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>{result.message}</AlertDescription>
                </Alert>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-3">Project Details</h3>
                    <div className="space-y-2 text-sm">
                      <div><Badge variant="outline">Name</Badge> {result.project.name}</div>
                      <div><Badge variant="outline">Slug</Badge> {result.project.slug}</div>
                      <div><Badge variant="outline">Atlas App</Badge> {result.atlasProject?.name || 'Created'}</div>
                      {result.chat?.id && (
                        <div><Badge variant="outline">Chat ID</Badge> {result.chat.id.slice(0, 8)}...</div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-3">Integration Status</h3>
                    <div className="space-y-2 text-sm">
                      <div><Badge variant="outline">OAuth</Badge> ✅ Configured</div>
                      <div><Badge variant="outline">Database</Badge> ✅ MongoDB Atlas Ready</div>
                      <div><Badge variant="outline">Real-time</Badge> ✅ Device Sync Enabled</div>
                      <div><Badge variant="outline">Auth</Badge> ✅ Magically.life</div>
                      <div><Badge variant="outline">Files</Badge> ✅ Saved to Project</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold mb-3">Next Steps</h3>
                  <ol className="list-decimal list-inside text-sm space-y-1">
                    {result.instructions?.map((instruction: string, index: number) => (
                      <li key={index}>{instruction}</li>
                    ))}
                  </ol>
                </div>

                <div className="flex gap-4">
                  <Button
                    onClick={() => window.open(`/projects`, '_blank')}
                    size="lg"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View All Projects
                  </Button>
                  {result.project?.id && (
                    <Button
                      variant="outline"
                      onClick={() => window.open(`/projects/${result.project.id}`, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open This Project
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    onClick={() => {
                      setResult(null);
                      setAppName('');
                      setAppIdea('');
                    }}
                  >
                    Create Another App
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="oauth" className="space-y-6">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Test OAuth Flow</CardTitle>
              <CardDescription>
                Test the OAuth authentication flow with your latest MongoDB Atlas app
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {loadingLatest ? (
                <div className="text-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Loading latest app...</p>
                </div>
              ) : latestApp ? (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">Latest App</h3>
                    <div className="space-y-1 text-sm">
                      <div><Badge variant="outline">Name</Badge> {latestApp.appName}</div>
                      <div><Badge variant="outline">Client ID</Badge> {latestApp.oauthClientId || 'Not configured'}</div>
                      <div><Badge variant="outline">Atlas App</Badge> {latestApp.mongodbAtlasAppId || 'Not configured'}</div>
                    </div>
                  </div>

                  <Button
                    onClick={testOAuthEndpoints}
                    disabled={!latestApp.oauthClientId}
                    className="w-full"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Test OAuth Flow
                  </Button>

                  {!latestApp.oauthClientId && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        No OAuth client found. Create an app first in the "Create App" tab.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No apps found. Create an app first in the "Create App" tab.
                  </AlertDescription>
                </Alert>
              )}

              <Button
                variant="outline"
                onClick={loadLatestApp}
                disabled={loadingLatest}
                className="w-full"
              >
                {loadingLatest ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  'Refresh Latest App'
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="docs" className="space-y-6">
          <Card className="max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle>MongoDB Atlas Integration API</CardTitle>
              <CardDescription>
                Documentation for the MongoDB Atlas backend integration
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="font-semibold mb-3">Key Features</h3>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li><strong>Database-Per-App:</strong> Each app gets its own MongoDB database for perfect isolation</li>
                  <li><strong>Single Atlas Project:</strong> Cost-efficient shared infrastructure</li>
                  <li><strong>No API Calls:</strong> Simple configuration-based setup, no Atlas API required</li>
                  <li><strong>Device Sync:</strong> Real-time data synchronization for mobile apps</li>
                  <li><strong>Realm SDK:</strong> TypeScript-first mobile database with offline support</li>
                  <li><strong>OAuth Integration:</strong> Centralized authentication with Magically.life</li>
                  <li><strong>Easy Setup:</strong> Just provide Project ID, Cluster Name, and App Services ID</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold mb-3">Database-Per-App Architecture</h3>
                <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
{`Single Atlas Project:
├── Database: app_123_taskmaster     # App 1 data
├── Database: app_456_chatapp        # App 2 data
├── Database: app_789_shopease       # App 3 data
└── Database: app_999_fittracker     # App 4 data

Generated App Structure:
├── App.tsx                    # Main app with Realm provider
├── realm.ts                   # Connects to app-specific database
├── auth.tsx                   # OAuth authentication service
├── screens/
│   ├── HomeScreen.tsx         # Real-time data with Device Sync
│   └── LoginScreen.tsx        # Branded login screen
├── models/
│   └── schemas.ts             # Realm object schemas
└── package.json               # Dependencies with @realm/react`}
                </pre>
              </div>

              <div>
                <h3 className="font-semibold mb-3">API Endpoints</h3>
                <div className="space-y-3">
                  <div className="border rounded-lg p-3">
                    <div className="font-mono text-sm mb-2">POST /api/project</div>
                    <div className="text-sm text-muted-foreground">
                      Create a new project with MongoDB Atlas backend
                    </div>
                    <pre className="bg-muted p-2 rounded text-xs mt-2">
{`{
  "message": "Create a TaskMaster: A todo app...",
  "backend": "mongodb-atlas"
}`}
                    </pre>
                  </div>

                  <div className="border rounded-lg p-3">
                    <div className="font-mono text-sm mb-2">GET /api/oauth/authorize</div>
                    <div className="text-sm text-muted-foreground">
                      OAuth authorization endpoint for generated apps
                    </div>
                  </div>

                  <div className="border rounded-lg p-3">
                    <div className="font-mono text-sm mb-2">POST /api/oauth/token</div>
                    <div className="text-sm text-muted-foreground">
                      OAuth token exchange endpoint
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-3">Advantages over Convex</h3>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li><strong>Simpler Functions:</strong> Standard HTTP handlers vs complex mutations/actions</li>
                  <li><strong>Better Debugging:</strong> Rich Atlas dashboard vs poor Convex logs</li>
                  <li><strong>AI-Friendly:</strong> Standard JavaScript patterns vs Convex-specific syntax</li>
                  <li><strong>90% Lower Cost:</strong> Database-per-app vs project-per-app</li>
                  <li><strong>Perfect Isolation:</strong> Separate databases with shared infrastructure</li>
                  <li><strong>Offline-First:</strong> Built-in offline support with Realm</li>
                  <li><strong>Easier Management:</strong> Single Atlas project vs hundreds of Convex projects</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
