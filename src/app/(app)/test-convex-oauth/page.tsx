'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, CheckCircle, AlertCircle, Download, ExternalLink } from 'lucide-react';

export default function TestConvexOAuthPage() {
  const [appName, setAppName] = useState('');
  const [appIdea, setAppIdea] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingLatest, setLoadingLatest] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [latestApp, setLatestApp] = useState<any>(null);

  // Test data for quick testing
  const testApps = [
    {
      name: 'TaskMaster',
      idea: 'A todo app where users can create tasks, mark them as complete, set priorities, and organize by categories'
    },
    {
      name: 'ChatHub',
      idea: 'A messaging app where users can create channels, send messages, and have real-time conversations'
    },
    {
      name: 'ShopEasy',
      idea: 'An e-commerce app where users can browse products, add to cart, and place orders'
    },
    {
      name: 'FitTracker',
      idea: 'A fitness app where users can log workouts, track exercises, and monitor their progress'
    }
  ];

  const handleCreateApp = async () => {
    if (!appName.trim() || !appIdea.trim()) {
      setError('Please provide both app name and description');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Use the standard project creation flow
      const response = await fetch('/api/project', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: `Create a ${appName.trim()}: ${appIdea.trim()}`,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}: Failed to create project`);
      }

      // Format the response to match our expected structure
      setResult({
        success: true,
        project: {
          id: data.id,
          name: data.appName,
          slug: data.slug,
        },
        message: `🎉 Your full-stack app "${data.appName}" has been created! Complete with Convex backend, TypeScript, and authentication.`,
        instructions: [
          '1. Your project has been created with Convex integration',
          '2. TypeScript files are ready in your project',
          '3. OAuth authentication is configured',
          '4. Visit the project to continue development',
        ],
      });
    } catch (err) {
      console.error('Create app error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const loadTestApp = (testApp: typeof testApps[0]) => {
    setAppName(testApp.name);
    setAppIdea(testApp.idea);
    setError(null);
    setResult(null);
  };



  // Load latest app on component mount
  React.useEffect(() => {
    loadLatestApp();
  }, []);

  const loadLatestApp = async () => {
    try {
      setLoadingLatest(true);
      const response = await fetch('/api/project?latest=true');
      if (response.ok) {
        const data = await response.json();
        if (data.project) {
          setLatestApp(data.project);
          // If we have a latest app, load its details
          if (data.project.convexProjectId) {
            setResult({
              project: data.project,
              convexProject: {
                name: data.project.convexProjectId,
                url: data.project.convexDeploymentUrl,
              },
              oauthConfig: {
                clientId: data.project.oauthClientId || 'loading...',
                authUrl: '/api/oauth/authorize',
                tokenUrl: '/api/oauth/token',
              },
              schema: data.project.schema || {},
              message: `Loaded existing app: ${data.project.appName}`,
            });
          }
        }
      }
    } catch (err) {
      console.error('Failed to load latest app:', err);
    } finally {
      setLoadingLatest(false);
    }
  };

  const testOAuthEndpoints = async () => {
    try {
      if (!latestApp?.oauthClientId) {
        setError('No OAuth client found. Create an app first.');
        return;
      }

      // Use the actual OAuth client from the latest app
      const redirectUri = encodeURIComponent('exp://localhost:19000/--/auth');
      const authUrl = `/api/oauth/authorize?client_id=${latestApp.oauthClientId}&redirect_uri=${redirectUri}&response_type=code&state=test`;

      window.open(authUrl, '_blank');
    } catch (err) {
      setError('Failed to test OAuth endpoints');
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">MongoDB Atlas + OAuth Integration Test</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Test the "Shopify for mobile apps" experience. Create a full-stack React Native app with MongoDB Atlas backend, authentication and real-time sync in minutes.
        </p>
      </div>

      <Tabs defaultValue="create" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="create">Create App</TabsTrigger>
          <TabsTrigger value="oauth">Test OAuth</TabsTrigger>
          <TabsTrigger value="docs">API Docs</TabsTrigger>
        </TabsList>

        <TabsContent value="create" className="space-y-6">
          {!result ? (
            <Card className="max-w-2xl mx-auto">
              <CardHeader>
                <CardTitle>Create Full-Stack App</CardTitle>
                <CardDescription>
                  Describe your app idea and get a complete React Native app with Convex backend
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-2">
                  <label className="text-sm font-medium">Quick Test Apps</label>
                  <div className="flex flex-wrap gap-2">
                    {testApps.map((testApp, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => loadTestApp(testApp)}
                        disabled={loading}
                      >
                        {testApp.name}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="appName" className="text-sm font-medium">App Name</label>
                  <Input
                    id="appName"
                    placeholder="e.g., TaskMaster, ChatApp, ShopEasy"
                    value={appName}
                    onChange={(e) => setAppName(e.target.value)}
                    disabled={loading}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="appIdea" className="text-sm font-medium">App Description</label>
                  <Textarea
                    id="appIdea"
                    placeholder="Describe your app idea in detail..."
                    value={appIdea}
                    onChange={(e) => setAppIdea(e.target.value)}
                    disabled={loading}
                    rows={4}
                  />
                </div>

                <Button 
                  onClick={handleCreateApp} 
                  disabled={loading || !appName.trim() || !appIdea.trim()}
                  className="w-full"
                  size="lg"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Your App... (This may take 30-60 seconds)
                    </>
                  ) : (
                    'Create Full-Stack App'
                  )}
                </Button>
              </CardContent>
            </Card>
          ) : (
            <Card className="max-w-4xl mx-auto">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-6 w-6 text-green-500" />
                  App Created Successfully!
                </CardTitle>
                <CardDescription>
                  Your full-stack app "{result.project.name}" is ready
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>{result.message}</AlertDescription>
                </Alert>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-3">Project Details</h3>
                    <div className="space-y-2 text-sm">
                      <div><Badge variant="outline">Name</Badge> {result.project.name}</div>
                      <div><Badge variant="outline">Slug</Badge> {result.project.slug}</div>
                      <div><Badge variant="outline">Convex</Badge> {result.convexProject.name}</div>
                      {result.chat?.id && (
                        <div><Badge variant="outline">Chat ID</Badge> {result.chat.id.slice(0, 8)}...</div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-3">Integration Status</h3>
                    <div className="space-y-2 text-sm">
                      <div><Badge variant="outline">OAuth</Badge> ✅ Configured</div>
                      <div><Badge variant="outline">Database</Badge> ✅ Convex Ready</div>
                      <div><Badge variant="outline">Auth</Badge> ✅ Magically.life</div>
                      <div><Badge variant="outline">Files</Badge> ✅ Saved to Project</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold mb-3">Next Steps</h3>
                  <ol className="list-decimal list-inside text-sm space-y-1">
                    {result.instructions?.map((instruction: string, index: number) => (
                      <li key={index}>{instruction}</li>
                    ))}
                  </ol>
                </div>

                <div className="flex gap-4">
                  <Button
                    onClick={() => window.open(`/projects`, '_blank')}
                    size="lg"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View All Projects
                  </Button>
                  {result.project?.id && (
                    <Button
                      variant="outline"
                      onClick={() => window.open(`/projects/${result.project.id}`, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open This Project
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    onClick={() => {
                      setResult(null);
                      setAppName('');
                      setAppIdea('');
                    }}
                  >
                    Create Another App
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="oauth" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>OAuth Flow Explanation</CardTitle>
                <CardDescription>How the authentication works</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3 text-sm">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="font-medium text-blue-900">1. /api/oauth/authorize</div>
                    <div className="text-blue-700">
                      • Validates app (client_id)
                      • Checks if user logged in to YOUR platform
                      • Redirects to /login if not authenticated
                      • Generates authorization code if authenticated
                    </div>
                  </div>

                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="font-medium text-green-900">2. /api/oauth/token</div>
                    <div className="text-green-700">
                      • Validates authorization code
                      • Verifies app credentials
                      • Generates app-scoped JWT token
                      • Returns access token to app
                    </div>
                  </div>

                  <div className="p-3 bg-purple-50 rounded-lg">
                    <div className="font-medium text-purple-900">3. User Experience</div>
                    <div className="text-purple-700">
                      • User clicks "Sign In" in generated app
                      • Browser opens to magically.life
                      • User sees YOUR branded login page
                      • After login, redirected back to app
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Test OAuth Flow</CardTitle>
                <CardDescription>
                  {latestApp ? `Test with: ${latestApp.appName}` : 'Create an app first to test OAuth'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {loadingLatest && (
                  <Alert>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <AlertDescription>Loading latest app...</AlertDescription>
                  </Alert>
                )}

                {latestApp ? (
                  <div className="space-y-3">
                    <div className="space-y-2 text-sm">
                      <div><Badge variant="outline">App</Badge> {latestApp.appName}</div>
                      <div><Badge variant="outline">Client ID</Badge> {latestApp.oauthClientId?.slice(0, 12)}...</div>
                      <div><Badge variant="outline">Redirect URI</Badge> exp://localhost:19000/--/auth</div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Test URLs:</h4>
                      <div className="text-xs bg-muted p-2 rounded">
                        <div className="mb-1"><strong>Authorize:</strong></div>
                        <div className="break-all">
                          /api/oauth/authorize?client_id={latestApp.oauthClientId}&redirect_uri=exp://localhost:19000/--/auth&response_type=code
                        </div>
                      </div>
                    </div>

                    <Button onClick={testOAuthEndpoints} className="w-full">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Test OAuth Authorization
                    </Button>

                    <Alert>
                      <AlertDescription className="text-xs">
                        This will open the OAuth flow in a new tab. You'll see your login page if not authenticated,
                        then get redirected back with an authorization code.
                      </AlertDescription>
                    </Alert>
                  </div>
                ) : (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      No apps found. Create a full-stack app first to test OAuth.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="docs" className="space-y-6">
          <Card className="max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle>API Documentation</CardTitle>
              <CardDescription>Available endpoints and their usage</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="font-semibold mb-2">Create Full-Stack App</h3>
                <div className="bg-muted p-4 rounded-lg text-sm">
                  <div className="mb-2"><Badge>POST</Badge> /api/create-fullstack-app</div>
                  <pre>{JSON.stringify({
                    appName: "string",
                    appIdea: "string"
                  }, null, 2)}</pre>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">OAuth Authorization</h3>
                <div className="bg-muted p-4 rounded-lg text-sm">
                  <div className="mb-2"><Badge>GET</Badge> /api/oauth/authorize</div>
                  <div>Query params: client_id, redirect_uri, response_type, state</div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">OAuth Token Exchange</h3>
                <div className="bg-muted p-4 rounded-lg text-sm">
                  <div className="mb-2"><Badge>POST</Badge> /api/oauth/token</div>
                  <pre>{JSON.stringify({
                    grant_type: "authorization_code",
                    code: "string",
                    client_id: "string",
                    redirect_uri: "string"
                  }, null, 2)}</pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
