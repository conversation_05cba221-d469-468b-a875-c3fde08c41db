'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, Database } from 'lucide-react';

export default function InstantDbAdminPage() {
  const [isConfigured, setIsConfigured] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [clientId, setClientId] = useState('');
  const [clientSecret, setClientSecret] = useState('');

  useEffect(() => {
    checkConfiguration();
  }, []);

  const checkConfiguration = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/instantdb/status');
      const data = await response.json();
      setIsConfigured(data.configured);
    } catch (err) {
      setError('Failed to check configuration status');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetup = async () => {
    if (!clientId.trim() || !clientSecret.trim()) {
      setError('Please provide both Client ID and Client Secret');
      return;
    }

    try {
      setIsSaving(true);
      setError(null);

      const response = await fetch('/api/admin/instantdb/setup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ clientId: clientId.trim(), clientSecret: clientSecret.trim() }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Setup failed');
      }

      setSuccess('InstantDB Platform configured successfully');
      setIsConfigured(true);
      setClientId('');
      setClientSecret('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Setup failed');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReload = async () => {
    try {
      setIsSaving(true);
      setError(null);

      const response = await fetch('/api/admin/instantdb/reload', {
        method: 'POST',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Reload failed');
      }

      setSuccess('Configuration reloaded successfully');
      await checkConfiguration();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Reload failed');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <div className="flex items-center space-x-2 mb-6">
        <Database className="h-6 w-6" />
        <h1 className="text-2xl font-bold">InstantDB Platform Setup</h1>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <XCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="mb-4">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Platform Configuration</CardTitle>
          <CardDescription>
            {isConfigured
              ? 'InstantDB Platform is configured and ready for app provisioning'
              : 'Configure InstantDB Platform OAuth credentials for automatic app provisioning'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isConfigured ? (
            <div className="space-y-4">
              <div className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="h-5 w-5" />
                <span>Platform configured successfully</span>
              </div>
              <Button
                onClick={handleReload}
                disabled={isSaving}
                variant="outline"
              >
                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Reload Configuration
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="clientId">Client ID</Label>
                <Input
                  id="clientId"
                  value={clientId}
                  onChange={(e) => setClientId(e.target.value)}
                  placeholder="Enter InstantDB Platform Client ID"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="clientSecret">Client Secret</Label>
                <Input
                  id="clientSecret"
                  type="password"
                  value={clientSecret}
                  onChange={(e) => setClientSecret(e.target.value)}
                  placeholder="Enter InstantDB Platform Client Secret"
                />
              </div>
              <Button
                onClick={handleSetup}
                disabled={isSaving}
              >
                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Setup Platform
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}