'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, RefreshCw, Settings, Database } from 'lucide-react';

interface InstantDbPlatformConfig {
  id: string;
  clientId: string;
  clientSecret: string;
  accessToken?: string;
  refreshToken?: string;
  tokenExpiresAt?: string;
  isActive: boolean;
  lastRefreshedAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface InstantDbApp {
  id: string;
  projectId: string;
  instantDbAppId: string;
  appName: string;
  status: 'active' | 'inactive' | 'error';
  lastSyncedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export default function InstantDbAdminPage() {
  const [config, setConfig] = useState<InstantDbPlatformConfig | null>(null);
  const [apps, setApps] = useState<InstantDbApp[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state for configuration
  const [clientId, setClientId] = useState('');
  const [clientSecret, setClientSecret] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load platform configuration
      const configResponse = await fetch('/api/admin/instantdb/config');
      if (configResponse.ok) {
        const configData = await configResponse.json();
        setConfig(configData);
        if (configData) {
          setClientId(configData.clientId);
          setClientSecret(configData.clientSecret);
        }
      }

      // Load InstantDB apps
      const appsResponse = await fetch('/api/admin/instantdb/apps');
      if (appsResponse.ok) {
        const appsData = await appsResponse.json();
        setApps(appsData);
      }
    } catch (err) {
      setError('Failed to load InstantDB data');
      console.error('Error loading InstantDB data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfigureOAuth = async () => {
    if (!clientId.trim() || !clientSecret.trim()) {
      setError('Please provide both Client ID and Client Secret');
      return;
    }

    try {
      setIsConfiguring(true);
      setError(null);

      const response = await fetch('/api/admin/instantdb/configure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientId: clientId.trim(),
          clientSecret: clientSecret.trim(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to configure OAuth');
      }

      setSuccess('OAuth configuration saved successfully');
      await loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to configure OAuth');
    } finally {
      setIsConfiguring(false);
    }
  };

  const handleRefreshTokens = async () => {
    try {
      setIsRefreshing(true);
      setError(null);

      const response = await fetch('/api/admin/instantdb/refresh-tokens', {
        method: 'POST',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to refresh tokens');
      }

      setSuccess('Tokens refreshed successfully');
      await loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh tokens');
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center space-x-2">
        <Database className="h-6 w-6" />
        <h1 className="text-2xl font-bold">InstantDB Platform Management</h1>
      </div>

      {error && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Platform Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Platform OAuth Configuration</span>
          </CardTitle>
          <CardDescription>
            Configure InstantDB Platform OAuth credentials for app provisioning
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="clientId">Client ID</Label>
              <Input
                id="clientId"
                value={clientId}
                onChange={(e) => setClientId(e.target.value)}
                placeholder="Enter InstantDB Client ID"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="clientSecret">Client Secret</Label>
              <Input
                id="clientSecret"
                type="password"
                value={clientSecret}
                onChange={(e) => setClientSecret(e.target.value)}
                placeholder="Enter InstantDB Client Secret"
              />
            </div>
          </div>

          <div className="flex space-x-2">
            <Button 
              onClick={handleConfigureOAuth}
              disabled={isConfiguring}
            >
              {isConfiguring && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {config ? 'Update Configuration' : 'Configure OAuth'}
            </Button>

            {config?.accessToken && (
              <Button 
                variant="outline"
                onClick={handleRefreshTokens}
                disabled={isRefreshing}
              >
                {isRefreshing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Tokens
              </Button>
            )}
          </div>

          {config && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Current Configuration</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div>Status: {getStatusBadge(config.isActive ? 'active' : 'inactive')}</div>
                <div>Has Access Token: {config.accessToken ? '✅' : '❌'}</div>
                <div>Token Expires: {formatDate(config.tokenExpiresAt)}</div>
                <div>Last Refreshed: {formatDate(config.lastRefreshedAt)}</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      {/* InstantDB Apps */}
      <Card>
        <CardHeader>
          <CardTitle>Provisioned InstantDB Apps</CardTitle>
          <CardDescription>
            Apps created through the Magically platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          {apps.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No InstantDB apps have been provisioned yet
            </div>
          ) : (
            <div className="space-y-4">
              {apps.map((app) => (
                <div key={app.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{app.appName}</h4>
                    {getStatusBadge(app.status)}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                    <div>App ID: {app.instantDbAppId}</div>
                    <div>Project ID: {app.projectId}</div>
                    <div>Last Synced: {formatDate(app.lastSyncedAt)}</div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
