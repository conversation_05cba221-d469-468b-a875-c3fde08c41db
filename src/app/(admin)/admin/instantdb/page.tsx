'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, Database } from 'lucide-react';

export default function InstantDbAdminPage() {
  const [isConfigured, setIsConfigured] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);



  useEffect(() => {
    checkConfiguration();

    // Check for OAuth callback results
    const urlParams = new URLSearchParams(window.location.search);
    const successParam = urlParams.get('success');
    const errorParam = urlParams.get('error');

    if (successParam === 'authorized') {
      setSuccess('Successfully authorized with InstantDB Platform');
      // Clean up URL
      window.history.replaceState({}, '', window.location.pathname);
    } else if (errorParam) {
      setError(`Authorization failed: ${errorParam}`);
      // Clean up URL
      window.history.replaceState({}, '', window.location.pathname);
    }
  }, []);

  const checkConfiguration = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/instantdb/status');
      const data = await response.json();
      setIsConfigured(data.configured);
    } catch (err) {
      setError('Failed to check configuration status');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAuthorize = async () => {
    try {
      setIsSaving(true);
      setError(null);

      const response = await fetch('/api/admin/instantdb/authorize-url');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get authorization URL');
      }

      // Redirect to InstantDB OAuth
      window.location.href = data.authUrl;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Authorization failed');
      setIsSaving(false);
    }
  };

  const handleReload = async () => {
    try {
      setIsSaving(true);
      setError(null);

      const response = await fetch('/api/admin/instantdb/reload', {
        method: 'POST',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Reload failed');
      }

      setSuccess('Configuration reloaded successfully');
      await checkConfiguration();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Reload failed');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <div className="flex items-center space-x-2 mb-6">
        <Database className="h-6 w-6" />
        <h1 className="text-2xl font-bold">InstantDB Platform Setup</h1>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <XCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="mb-4">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Platform Configuration</CardTitle>
          <CardDescription>
            {isConfigured
              ? 'InstantDB Platform is configured and ready for app provisioning'
              : 'Configure InstantDB Platform OAuth credentials for automatic app provisioning'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isConfigured ? (
            <div className="space-y-4">
              <div className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="h-5 w-5" />
                <span>Platform configured successfully</span>
              </div>
              <Button
                onClick={handleReload}
                disabled={isSaving}
                variant="outline"
              >
                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Reload Configuration
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Click the button below to authorize Magically with InstantDB Platform.
                This will allow us to automatically provision InstantDB apps for your users.
              </p>
              <Button
                onClick={handleAuthorize}
                disabled={isSaving}
              >
                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Authorize with InstantDB
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}