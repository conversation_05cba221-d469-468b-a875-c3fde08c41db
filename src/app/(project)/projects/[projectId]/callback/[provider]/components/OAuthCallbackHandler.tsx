'use client';

import { useEffect, useState } from 'react';

interface OAuthCallbackHandlerProps {
  projectId: string;
  provider: string;
}

/**
 * Exchange authorization code for access token
 * This calls our OAuth token endpoint
 */
async function exchangeCodeForToken(code: string, projectId: string) {
  try {
    console.log('Getting OAuth client for project:', projectId);

    // Get the OAuth client ID for this project
    const clientResponse = await fetch(`/api/project/${projectId}/oauth-client`);
    console.log('Client response status:', clientResponse.status);

    if (!clientResponse.ok) {
      const errorText = await clientResponse.text();
      console.error('Failed to get OAuth client:', clientResponse.status, errorText);
      throw new Error(`Failed to get OAuth client information: ${clientResponse.status}`);
    }
    const { clientId } = await clientResponse.json();
    console.log('Got client ID:', clientId);

    // Exchange code for token
    const redirectUri = `${window.location.origin}/projects/${projectId}/callback/magically`;
    console.log('Exchanging token with:', { clientId, redirectUri });

    const response = await fetch('/api/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        grant_type: 'authorization_code',
        code,
        client_id: clientId,
        redirect_uri: redirectUri,
      }),
    });

    console.log('Token response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Token exchange failed:', response.status, errorData);
      throw new Error(`Token exchange failed: ${errorData.error_description || response.status}`);
    }

    const tokenData = await response.json();

    if (tokenData.access_token) {
      // Decode JWT to get user info (simple decode, not verification)
      const payload = JSON.parse(atob(tokenData.access_token.split('.')[1]));
      const userData = {
        id: payload.sub,
        email: payload.email || '<EMAIL>',
        name: payload.name || payload.given_name || 'User',
        given_name: payload.given_name,
        family_name: payload.family_name,
      };

      return {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        expires_in: tokenData.expires_in,
        user: userData
      };
    } else {
      throw new Error('No access token received');
    }
  } catch (error) {
    console.error('Token exchange failed:', error);
    throw error;
  }
}

export default function OAuthCallbackHandler({ projectId, provider }: OAuthCallbackHandlerProps) {
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const processOAuthCallback = async () => {
      try {
        if(provider === "google") {
          // Extract tokens from URL
          const hashParams = new URLSearchParams(window.location.hash.substring(1));
          const queryParams = new URLSearchParams(window.location.search);

          // Check for tokens in hash (implicit flow) or query params (code flow)
          const accessToken = hashParams.get('access_token') || queryParams.get('access_token');
          const refreshToken = hashParams.get('refresh_token') || queryParams.get('refresh_token');
          const error = hashParams.get('error') || queryParams.get('error');

          if (error) {
            console.error('OAuth error:', error);
            setStatus('error');
            setErrorMessage(error);
            return;
          }

          if (!accessToken) {
            console.log('No access token found, this might be the initial redirect');
            return;
          }

          console.log('Tokens extracted successfully');

          // Send tokens to parent window if this is a popup
          if (window.opener) {
            window.opener.postMessage({
              type: 'supabase-auth-callback',
              projectId,
              provider,
              accessToken,
              refreshToken
            }, '*'); // In production, specify exact origin for security

            setStatus('success');

            // Close the popup after a short delay
            setTimeout(() => {
              window.close();
            }, 1500);
          } else {
            // If not in a popup, we're likely in the main window or iframe
            // Try to communicate with parent frames
            window.parent.postMessage({
              type: 'supabase-auth-callback',
              projectId,
              provider,
              accessToken,
              refreshToken
            }, '*');

            setStatus('success');

            // Redirect back to the project page after a delay
            setTimeout(() => {
              window.location.href = `/projects/${projectId}`;
            }, 3000);
          }
        } else if(provider === "magically") {
          // Extract authorization code from URL
          const queryParams = new URLSearchParams(window.location.search);
          const code = queryParams.get('code');
          const state = queryParams.get('state');
          const error = queryParams.get('error');

          if (error) {
            console.error('Magically OAuth error:', error);
            setStatus('error');
            setErrorMessage(error);
            return;
          }

          if (!code) {
            console.log('No authorization code found');
            setStatus('error');
            setErrorMessage('No authorization code received');
            return;
          }

          console.log('Authorization code received, exchanging for token...');

          // Exchange code for token
          console.log('Exchanging code for token...', { code: code.slice(0, 8) + '...', projectId });
          const tokenData = await exchangeCodeForToken(code, projectId);

          if (tokenData.access_token) {
            console.log('Token exchange successful');

            // Send tokens to parent window if this is a popup
            if (window.opener) {
              window.opener.postMessage({
                type: 'magically-auth-callback',
                projectId,
                provider,
                accessToken: tokenData.access_token,
                refreshToken: tokenData.refresh_token,
                user: tokenData.user
              }, '*'); // In production, specify exact origin for security

              setStatus('success');

              // Close the popup after a short delay
              setTimeout(() => {
                window.close();
              }, 1500);
            } else {
              // If not in a popup, communicate with parent frames
              window.parent.postMessage({
                type: 'magically-auth-callback',
                projectId,
                provider,
                accessToken: tokenData.access_token,
                refreshToken: tokenData.refresh_token,
                user: tokenData.user
              }, '*');

              setStatus('success');

              // Redirect back to the project page after a delay
              setTimeout(() => {
                window.location.href = `/projects/${projectId}`;
              }, 3000);
            }
          } else {
            throw new Error('No access token received from token exchange');
          }
        }

      } catch (error) {
        console.error('Error processing OAuth callback:', error);
        setStatus('error');
        setErrorMessage(error instanceof Error ? error.message : 'Unknown error');
      }
    };
    
    processOAuthCallback();
  }, [projectId, provider]);
  
  return (
    <div className="my-4">
      {status === 'processing' && (
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
          <p>Processing authentication...</p>
        </div>
      )}
      
      {status === 'success' && (
        <div className="text-green-500 flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          <p>Authentication successful!</p>
          <p className="text-sm text-muted-foreground mt-1">Returning to application...</p>
        </div>
      )}
      
      {status === 'error' && (
        <div className="text-red-500 flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
          <p>Authentication failed</p>
          {errorMessage && <p className="text-sm mt-1">{errorMessage}</p>}
          <button 
            className="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors"
            onClick={() => window.close()}
          >
            Close Window
          </button>
        </div>
      )}
    </div>
  );
}
