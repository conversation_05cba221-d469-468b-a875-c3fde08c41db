#!/usr/bin/env node

/**
 * Comprehensive test script for LLM API endpoints
 * Tests all permutations and combinations of the LLM API
 * Usage: npx tsx src/scripts/test-llm-api.ts
 */

// Load environment variables
require('dotenv').config({ path: ".env.local" });

import * as fs from 'fs';
import * as path from 'path';

// Test configuration
const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
const RESULTS_DIR = path.join(__dirname, '../../results/llm-api-tests');

// Ensure results directory exists
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

interface TestResult {
  testName: string;
  endpoint: string;
  method: string;
  requestBody: any;
  response: any;
  status: number;
  success: boolean;
  executionTime: number;
  error?: string;
  validationResults: {
    hasExpectedFields: boolean;
    responseFormat: string;
    dataTypes: Record<string, string>;
  };
}

interface TestSuite {
  suiteName: string;
  tests: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    averageTime: number;
  };
}

/**
 * Utility function to make API requests
 */
async function makeRequest(endpoint: string, body: any): Promise<{ response: any; status: number; time: number }> {
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body)
    });

    const data = await response.json();
    const executionTime = Date.now() - startTime;

    return {
      response: data,
      status: response.status,
      time: executionTime
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;
    throw {
      error: error instanceof Error ? error.message : 'Unknown error',
      time: executionTime
    };
  }
}

/**
 * Validate response format and data types
 */
function validateResponse(response: any, expectedFormat: string): any {
  const validation = {
    hasExpectedFields: false,
    responseFormat: typeof response,
    dataTypes: {} as Record<string, string>
  };

  if (typeof response === 'object' && response !== null) {
    // Record all field types
    Object.keys(response).forEach(key => {
      validation.dataTypes[key] = typeof response[key];
    });

    // Check expected formats
    switch (expectedFormat) {
      case 'plain_text':
        validation.hasExpectedFields = 'text' in response && typeof response.text === 'string';
        break;
      case 'structured':
        validation.hasExpectedFields = Object.keys(response).length > 0 && !('text' in response);
        break;
      case 'chat':
        validation.hasExpectedFields = 'message' in response && 'usage' in response;
        break;
      case 'image_single':
        validation.hasExpectedFields = 'url' in response && 'filename' in response && 'prompt' in response && 'downloadUrl' in response;
        break;
      case 'image_multiple':
        validation.hasExpectedFields = 'images' in response && 'count' in response && Array.isArray(response.images) &&
          response.images.every((img: any) => 'url' in img && 'filename' in img && 'downloadUrl' in img);
        break;
      case 'models':
        validation.hasExpectedFields = 'text' in response && 'image' in response && 'default' in response &&
          Array.isArray(response.text) && Array.isArray(response.image);
        break;
      case 'error':
        validation.hasExpectedFields = 'error' in response && typeof response.error === 'string';
        break;
    }
  }

  return validation;
}

/**
 * Test /api/llm/invoke endpoint with all permutations
 */
async function testInvokeEndpoint(): Promise<TestSuite> {
  console.log('🧪 Testing /api/llm/invoke endpoint...');
  
  const tests: TestResult[] = [];
  
  // Test cases for invoke endpoint
  const testCases = [
    {
      name: 'Plain text - simple prompt',
      body: {
        prompt: 'Say hello in exactly 5 words',
        temperature: 0.1
      },
      expectedFormat: 'plain_text'
    },
    {
      name: 'Plain text - with model specification',
      body: {
        prompt: 'Explain quantum computing in one sentence',
        model: 'openai/gpt-4o-mini',
        temperature: 0.7
      },
      expectedFormat: 'plain_text'
    },
    {
      name: 'Structured output - simple schema',
      body: {
        prompt: 'Create a user profile for John Doe, age 30, software engineer',
        response_json_schema: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            age: { type: 'number' },
            profession: { type: 'string' }
          }
        },
        temperature: 0.3
      },
      expectedFormat: 'structured'
    },
    {
      name: 'Structured output - complex nested schema',
      body: {
        prompt: 'Create a todo list with 3 tasks for a React developer',
        response_json_schema: {
          type: 'object',
          properties: {
            title: { type: 'string' },
            tasks: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number' },
                  task: { type: 'string' },
                  priority: { type: 'string' },
                  completed: { type: 'boolean' }
                }
              }
            },
            totalTasks: { type: 'number' }
          }
        },
        temperature: 0.5
      },
      expectedFormat: 'structured'
    },
    {
      name: 'Error case - missing prompt',
      body: {
        temperature: 0.7
      },
      expectedFormat: 'error'
    },
    {
      name: 'Error case - invalid temperature',
      body: {
        prompt: 'Test prompt',
        temperature: 2.0  // Invalid temperature > 1.0
      },
      expectedFormat: 'plain_text' // Should still work, temperature will be clamped
    }
  ];

  for (const testCase of testCases) {
    try {
      const { response, status, time } = await makeRequest('/api/llm/invoke', testCase.body);
      const validation = validateResponse(response, testCase.expectedFormat);
      
      tests.push({
        testName: testCase.name,
        endpoint: '/api/llm/invoke',
        method: 'POST',
        requestBody: testCase.body,
        response,
        status,
        success: testCase.expectedFormat === 'error' ? status >= 400 : status === 200,
        executionTime: time,
        validationResults: validation
      });

      console.log(`  ✅ ${testCase.name} - ${time}ms`);
    } catch (error: any) {
      tests.push({
        testName: testCase.name,
        endpoint: '/api/llm/invoke',
        method: 'POST',
        requestBody: testCase.body,
        response: null,
        status: 0,
        success: false,
        executionTime: error.time || 0,
        error: error.error || 'Unknown error',
        validationResults: {
          hasExpectedFields: false,
          responseFormat: 'error',
          dataTypes: {}
        }
      });

      console.log(`  ❌ ${testCase.name} - Error: ${error.error}`);
    }
  }

  const passed = tests.filter(t => t.success).length;
  const avgTime = tests.reduce((sum, t) => sum + t.executionTime, 0) / tests.length;

  return {
    suiteName: 'Invoke Endpoint Tests',
    tests,
    summary: {
      total: tests.length,
      passed,
      failed: tests.length - passed,
      averageTime: Math.round(avgTime)
    }
  };
}

/**
 * Test /api/llm/chat endpoint with all permutations
 */
async function testChatEndpoint(): Promise<TestSuite> {
  console.log('🧪 Testing /api/llm/chat endpoint...');

  const tests: TestResult[] = [];

  const testCases = [
    {
      name: 'Simple chat - single message',
      body: {
        messages: [
          { role: 'user', content: 'What is 2+2?' }
        ],
        stream: false,
        temperature: 0.1
      },
      expectedFormat: 'chat'
    },
    {
      name: 'Chat with system message',
      body: {
        messages: [
          { role: 'system', content: 'You are a helpful math tutor. Answer in exactly one sentence.' },
          { role: 'user', content: 'Explain what multiplication is' }
        ],
        stream: false,
        temperature: 0.3
      },
      expectedFormat: 'chat'
    },
    {
      name: 'Multi-turn conversation',
      body: {
        messages: [
          { role: 'system', content: 'You are a coding assistant.' },
          { role: 'user', content: 'How do I create a React component?' },
          { role: 'assistant', content: 'You can create a React component using function syntax...' },
          { role: 'user', content: 'Can you show me an example?' }
        ],
        stream: false,
        temperature: 0.5
      },
      expectedFormat: 'chat'
    },
    {
      name: 'Chat with different model',
      body: {
        messages: [
          { role: 'user', content: 'Write a haiku about coding' }
        ],
        model: 'openai/gpt-4o-mini',
        stream: false,
        temperature: 0.8
      },
      expectedFormat: 'chat'
    },
    {
      name: 'Error case - missing messages',
      body: {
        stream: false,
        temperature: 0.7
      },
      expectedFormat: 'error'
    },
    {
      name: 'Error case - empty messages array',
      body: {
        messages: [],
        stream: false
      },
      expectedFormat: 'error'
    },
    {
      name: 'Error case - invalid message format',
      body: {
        messages: [
          { role: 'invalid_role', content: 'Test message' }
        ],
        stream: false
      },
      expectedFormat: 'error' // Should return validation error
    }
  ];

  for (const testCase of testCases) {
    try {
      const { response, status, time } = await makeRequest('/api/llm/chat', testCase.body);
      const validation = validateResponse(response, testCase.expectedFormat);

      tests.push({
        testName: testCase.name,
        endpoint: '/api/llm/chat',
        method: 'POST',
        requestBody: testCase.body,
        response,
        status,
        success: testCase.expectedFormat === 'error' ? status >= 400 : status === 200,
        executionTime: time,
        validationResults: validation
      });

      console.log(`  ✅ ${testCase.name} - ${time}ms`);
    } catch (error: any) {
      tests.push({
        testName: testCase.name,
        endpoint: '/api/llm/chat',
        method: 'POST',
        requestBody: testCase.body,
        response: null,
        status: 0,
        success: false,
        executionTime: error.time || 0,
        error: error.error || 'Unknown error',
        validationResults: {
          hasExpectedFields: false,
          responseFormat: 'error',
          dataTypes: {}
        }
      });

      console.log(`  ❌ ${testCase.name} - Error: ${error.error}`);
    }
  }

  const passed = tests.filter(t => t.success).length;
  const avgTime = tests.reduce((sum, t) => sum + t.executionTime, 0) / tests.length;

  return {
    suiteName: 'Chat Endpoint Tests',
    tests,
    summary: {
      total: tests.length,
      passed,
      failed: tests.length - passed,
      averageTime: Math.round(avgTime)
    }
  };
}

/**
 * Test /api/llm/image endpoint with all permutations
 */
async function testImageEndpoint(): Promise<TestSuite> {
  console.log('🧪 Testing /api/llm/image endpoint...');

  const tests: TestResult[] = [];

  const testCases = [
    {
      name: 'Single image - simple prompt',
      body: {
        prompt: 'A simple geometric pattern with blue and white colors',
        size: '1024x1024',
        quality: 'standard'
      },
      expectedFormat: 'image_single'
    },
    {
      name: 'Single image - DALL-E 3 with HD quality',
      body: {
        prompt: 'A minimalist icon representing React components',
        model: 'dall-e-3',
        size: '1024x1024',
        quality: 'hd'
      },
      expectedFormat: 'image_single'
    },
    {
      name: 'Single image - different size',
      body: {
        prompt: 'A landscape view of mountains at sunset',
        model: 'dall-e-3',
        size: '1792x1024',
        quality: 'standard'
      },
      expectedFormat: 'image_single'
    },
    {
      name: 'Multiple images - DALL-E 2',
      body: {
        prompt: 'Simple geometric pattern',
        model: 'dall-e-2',
        size: '1024x1024',
        n: 2
      },
      expectedFormat: 'image_multiple'
    },
    {
      name: 'Error case - missing prompt',
      body: {
        size: '1024x1024',
        quality: 'standard'
      },
      expectedFormat: 'error'
    },
    {
      name: 'Error case - invalid size',
      body: {
        prompt: 'Test image',
        size: '500x500', // Invalid size
        quality: 'standard'
      },
      expectedFormat: 'error'
    },
    {
      name: 'Error case - invalid model',
      body: {
        prompt: 'Test image',
        model: 'invalid-model',
        size: '1024x1024'
      },
      expectedFormat: 'error'
    }
  ];

  for (const testCase of testCases) {
    try {
      const { response, status, time } = await makeRequest('/api/llm/image', testCase.body);
      const validation = validateResponse(response, testCase.expectedFormat);

      tests.push({
        testName: testCase.name,
        endpoint: '/api/llm/image',
        method: 'POST',
        requestBody: testCase.body,
        response: response, // No need to truncate anymore since we're using URLs
        status,
        success: testCase.expectedFormat === 'error' ? status >= 400 : status === 200,
        executionTime: time,
        validationResults: validation
      });

      console.log(`  ✅ ${testCase.name} - ${time}ms`);
    } catch (error: any) {
      tests.push({
        testName: testCase.name,
        endpoint: '/api/llm/image',
        method: 'POST',
        requestBody: testCase.body,
        response: null,
        status: 0,
        success: false,
        executionTime: error.time || 0,
        error: error.error || 'Unknown error',
        validationResults: {
          hasExpectedFields: false,
          responseFormat: 'error',
          dataTypes: {}
        }
      });

      console.log(`  ❌ ${testCase.name} - Error: ${error.error}`);
    }
  }

  const passed = tests.filter(t => t.success).length;
  const avgTime = tests.reduce((sum, t) => sum + t.executionTime, 0) / tests.length;

  return {
    suiteName: 'Image Endpoint Tests',
    tests,
    summary: {
      total: tests.length,
      passed,
      failed: tests.length - passed,
      averageTime: Math.round(avgTime)
    }
  };
}

/**
 * Test edge cases and error scenarios
 */
async function testEdgeCases(): Promise<TestSuite> {
  console.log('🧪 Testing edge cases and error scenarios...');

  const tests: TestResult[] = [];

  const testCases = [
    {
      name: 'Get available models',
      endpoint: '/api/llm/models',
      method: 'GET',
      body: {},
      expectedFormat: 'models'
    },
    {
      name: 'Invalid endpoint',
      endpoint: '/api/llm/invalid',
      body: { prompt: 'test' },
      expectedFormat: 'error'
    },
    {
      name: 'OPTIONS request to invoke',
      endpoint: '/api/llm/invoke',
      method: 'OPTIONS',
      body: {},
      expectedFormat: 'cors'
    },
    {
      name: 'GET request to chat (should fail)',
      endpoint: '/api/llm/chat',
      method: 'GET',
      body: {},
      expectedFormat: 'error'
    },
    {
      name: 'Large prompt test',
      endpoint: '/api/llm/invoke',
      body: {
        prompt: 'A'.repeat(10000), // Very large prompt
        temperature: 0.1
      },
      expectedFormat: 'plain_text'
    },
    {
      name: 'Empty JSON body',
      endpoint: '/api/llm/invoke',
      body: {},
      expectedFormat: 'error'
    }
  ];

  for (const testCase of testCases) {
    try {
      const method = testCase.method || 'POST';
      const startTime = Date.now();

      const response = await fetch(`${BASE_URL}${testCase.endpoint}`, {
        method,
        headers: method === 'POST' ? { 'Content-Type': 'application/json' } : {},
        body: method === 'POST' ? JSON.stringify(testCase.body) : undefined
      });

      const time = Date.now() - startTime;
      let data;

      try {
        data = await response.json();
      } catch {
        data = { status: response.status, statusText: response.statusText };
      }

      const validation = validateResponse(data, testCase.expectedFormat);

      tests.push({
        testName: testCase.name,
        endpoint: testCase.endpoint,
        method,
        requestBody: testCase.body,
        response: data,
        status: response.status,
        success: testCase.expectedFormat === 'error' ? response.status >= 400 :
                 testCase.expectedFormat === 'cors' ? response.status === 204 :
                 testCase.expectedFormat === 'models' ? response.status === 200 :
                 response.status === 200,
        executionTime: time,
        validationResults: validation
      });

      console.log(`  ✅ ${testCase.name} - ${time}ms (Status: ${response.status})`);
    } catch (error: any) {
      tests.push({
        testName: testCase.name,
        endpoint: testCase.endpoint,
        method: testCase.method || 'POST',
        requestBody: testCase.body,
        response: null,
        status: 0,
        success: false,
        executionTime: 0,
        error: error.message || 'Unknown error',
        validationResults: {
          hasExpectedFields: false,
          responseFormat: 'error',
          dataTypes: {}
        }
      });

      console.log(`  ❌ ${testCase.name} - Error: ${error.message}`);
    }
  }

  const passed = tests.filter(t => t.success).length;
  const avgTime = tests.reduce((sum, t) => sum + t.executionTime, 0) / tests.length;

  return {
    suiteName: 'Edge Cases and Error Tests',
    tests,
    summary: {
      total: tests.length,
      passed,
      failed: tests.length - passed,
      averageTime: Math.round(avgTime)
    }
  };
}

/**
 * Generate comprehensive test report
 */
function generateReport(testSuites: TestSuite[]): void {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

  // Calculate overall statistics
  const totalTests = testSuites.reduce((sum, suite) => sum + suite.summary.total, 0);
  const totalPassed = testSuites.reduce((sum, suite) => sum + suite.summary.passed, 0);
  const totalFailed = testSuites.reduce((sum, suite) => sum + suite.summary.failed, 0);
  const overallAvgTime = testSuites.reduce((sum, suite) => sum + suite.summary.averageTime, 0) / testSuites.length;

  // Console report
  console.log('\n' + '='.repeat(80));
  console.log('🎉 LLM API TEST RESULTS SUMMARY');
  console.log('='.repeat(80));
  console.log(`📊 Total Tests: ${totalTests}`);
  console.log(`✅ Passed: ${totalPassed} (${Math.round((totalPassed / totalTests) * 100)}%)`);
  console.log(`❌ Failed: ${totalFailed} (${Math.round((totalFailed / totalTests) * 100)}%)`);
  console.log(`⏱️  Average Response Time: ${Math.round(overallAvgTime)}ms`);
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log(`📅 Test Date: ${new Date().toLocaleString()}`);

  console.log('\n📋 Test Suite Breakdown:');
  testSuites.forEach(suite => {
    const passRate = Math.round((suite.summary.passed / suite.summary.total) * 100);
    console.log(`  ${suite.suiteName}: ${suite.summary.passed}/${suite.summary.total} (${passRate}%) - Avg: ${suite.summary.averageTime}ms`);
  });

  // Detailed JSON report
  const detailedReport = {
    metadata: {
      timestamp: new Date().toISOString(),
      baseUrl: BASE_URL,
      totalTests,
      totalPassed,
      totalFailed,
      passRate: Math.round((totalPassed / totalTests) * 100),
      averageResponseTime: Math.round(overallAvgTime)
    },
    testSuites,
    failedTests: testSuites.flatMap(suite =>
      suite.tests.filter(test => !test.success).map(test => ({
        suite: suite.suiteName,
        test: test.testName,
        error: test.error,
        endpoint: test.endpoint,
        status: test.status
      }))
    )
  };

  // Save detailed report
  const reportPath = path.join(RESULTS_DIR, `llm-api-test-report-${timestamp}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(detailedReport, null, 2));
  console.log(`\n📁 Detailed report saved: ${reportPath}`);

  // Save summary report
  const summaryPath = path.join(RESULTS_DIR, 'latest-summary.json');
  fs.writeFileSync(summaryPath, JSON.stringify(detailedReport.metadata, null, 2));
  console.log(`📁 Summary saved: ${summaryPath}`);

  // Performance analysis
  console.log('\n⚡ Performance Analysis:');
  const fastTests = testSuites.flatMap(s => s.tests).filter(t => t.executionTime < 2000);
  const slowTests = testSuites.flatMap(s => s.tests).filter(t => t.executionTime > 10000);

  console.log(`  Fast responses (<2s): ${fastTests.length}`);
  console.log(`  Slow responses (>10s): ${slowTests.length}`);

  if (slowTests.length > 0) {
    console.log('  Slowest tests:');
    slowTests.sort((a, b) => b.executionTime - a.executionTime).slice(0, 3).forEach(test => {
      console.log(`    - ${test.testName}: ${test.executionTime}ms`);
    });
  }
}

/**
 * Main test execution function
 */
async function runLLMAPITests(): Promise<void> {
  console.log('🚀 Starting Comprehensive LLM API Tests');
  console.log('==========================================');
  console.log(`🌐 Testing against: ${BASE_URL}`);
  console.log(`📁 Results will be saved to: ${RESULTS_DIR}`);
  console.log('');

  const testSuites: TestSuite[] = [];

  try {
    // Test all endpoints
    testSuites.push(await testInvokeEndpoint());
    testSuites.push(await testChatEndpoint());
    testSuites.push(await testImageEndpoint());
    testSuites.push(await testEdgeCases());

    // Generate comprehensive report
    generateReport(testSuites);

    console.log('\n🎉 All tests completed successfully!');
    console.log('💡 Tips:');
    console.log('  - Check the detailed JSON reports for full response data');
    console.log('  - Image tests may take longer due to generation time');
    console.log('  - Failed tests may indicate API key issues or server problems');

  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  runLLMAPITests()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}
