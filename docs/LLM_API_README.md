# LLM API Documentation

## Overview

The Magically LLM API provides built-in AI capabilities for user applications with model whitelisting, Vercel Blob storage integration, and comprehensive validation.

## Architecture

### Service Layer
- **Core Logic**: `src/lib/services/llm-api-service.ts`
- **Route Handlers**: `src/app/api/llm/[endpoint]/route.ts`
- **Clean separation**: Routes handle HTTP concerns, service handles business logic

### Model Whitelisting
Only approved models are allowed to ensure cost control and security:

**Text Models:**
- `openai/gpt-4.1-mini`
- `openai/gpt-4.1-nano` (default)
- `openai/gpt-4.1`
- `openai/gpt-4o-mini`
- `openai/gpt-o3-mini-high`
- `google/gemini-2.5-flash`
- `anthropic/claude-3.5-haiku`

**Image Models:**
- `dall-e-3` (default, single images)
- `dall-e-2` (multiple images)

## Endpoints

### GET /api/llm/models
Returns available models and defaults.

**Response:**
```json
{
  "text": ["openai/gpt-4.1-nano", "openai/gpt-4.1-mini", ...],
  "image": ["dall-e-3", "dall-e-2"],
  "default": {
    "text": "openai/gpt-4.1-nano",
    "image": "dall-e-3"
  }
}
```

### POST /api/llm/invoke
Text generation with optional structured output.

**Parameters:**
- `prompt` (required): Text prompt
- `response_json_schema` (optional): JSON schema for structured output
- `model` (optional): Whitelisted model name
- `temperature` (optional): 0.0-1.0, auto-clamped

**Examples:**
```typescript
// Plain text
{
  "prompt": "Write a tagline for a fitness app"
}

// Structured output
{
  "prompt": "Generate user profile",
  "response_json_schema": {
    "type": "object",
    "properties": {
      "name": { "type": "string" },
      "age": { "type": "number" }
    }
  }
}
```

### POST /api/llm/chat
Conversational interface with message history.

**Parameters:**
- `messages` (required): Array of message objects
- `model` (optional): Whitelisted model name
- `temperature` (optional): 0.0-1.0, auto-clamped
- `stream` (optional): Enable streaming response

**Message Format:**
```typescript
{
  "messages": [
    { "role": "system", "content": "You are a helpful assistant" },
    { "role": "user", "content": "Hello!" }
  ]
}
```

**Valid Roles:** `system`, `user`, `assistant`

### POST /api/llm/image
Image generation with Vercel Blob storage.

**Parameters:**
- `prompt` (required): Image description
- `model` (optional): `dall-e-2` or `dall-e-3`
- `size` (optional): `1024x1024`, `1792x1024`, `1024x1792`
- `quality` (optional): `standard` or `hd` (DALL-E 3 only)
- `n` (optional): Number of images 1-10 (DALL-E 2 only)

**Response:**
```typescript
// Single image
{
  "url": "https://blob.vercel-storage.com/llm-images/...",
  "filename": "llm-images/timestamp-dalle3.png",
  "downloadUrl": "https://blob.vercel-storage.com/...",
  "prompt": "Original prompt",
  "model": "dall-e-3"
}

// Multiple images (DALL-E 2)
{
  "images": [
    { "url": "...", "filename": "...", "downloadUrl": "..." }
  ],
  "count": 2
}
```

## Error Handling

All endpoints return descriptive error messages:

- **400**: Invalid parameters, missing required fields, non-whitelisted models
- **404**: Invalid endpoint
- **429**: Rate limit exceeded
- **500**: Generation failed, API issues

**Error Response:**
```json
{
  "error": "Model invalid-model is not allowed. Please contact Magically support to get it whitelisted."
}
```

## Validation Features

### Input Validation
- **Model Whitelisting**: Only approved models accepted
- **Temperature Clamping**: Invalid temperatures (e.g., 2.0) automatically clamped to valid range (0.0-1.0)
- **Message Format**: Chat messages validated for required `role` and `content` fields
- **Image Parameters**: Size, quality, and count validation

### Response Validation
- Consistent response formats across all endpoints
- Proper HTTP status codes
- CORS headers for cross-origin requests

## Storage Integration

### Vercel Blob
Images are automatically saved to Vercel Blob storage:
- **Public URLs**: Permanent, accessible image links
- **Organized Storage**: Files saved to `llm-images/` directory
- **Unique Naming**: Timestamp-based filenames prevent conflicts
- **Multiple Formats**: Both `url` and `downloadUrl` provided

## Performance

### Response Times
- **Text Generation**: 500-2000ms
- **Chat**: 600-1500ms
- **Image Generation**: 10-30 seconds (expected for AI image creation)
- **Models Endpoint**: <100ms

### Optimization
- **Concise Prompts**: Documentation optimized for token efficiency
- **Service Layer**: Clean architecture for maintainability
- **Validation**: Early validation prevents unnecessary API calls

## Testing

Comprehensive test suite with 26 tests covering:
- All endpoint permutations
- Error scenarios and edge cases
- Model validation
- Response format validation
- Performance monitoring

**Current Status:** 100% test success rate

## Usage Examples

### React Native Integration
```typescript
// Get available models
const models = await fetch('/api/llm/models').then(r => r.json());

// Generate text
const response = await fetch('/api/llm/invoke', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'Create app tagline',
    model: 'openai/gpt-4.1-nano'
  })
});

// Chat conversation
const chat = await fetch('/api/llm/chat', {
  method: 'POST',
  body: JSON.stringify({
    messages: [{ role: 'user', content: 'Hello' }]
  })
});

// Generate image
const image = await fetch('/api/llm/image', {
  method: 'POST',
  body: JSON.stringify({
    prompt: 'Modern app icon design'
  })
});
```

## Security

- **Model Whitelisting**: Prevents unauthorized model usage
- **Input Validation**: Comprehensive parameter validation
- **Error Messages**: Clear guidance without exposing internals
- **CORS Support**: Proper cross-origin request handling

## Monitoring

- **Test Reports**: Automated testing with detailed JSON reports
- **Performance Tracking**: Response time monitoring
- **Error Logging**: Comprehensive error logging and reporting
- **Usage Analytics**: Track endpoint usage and success rates

## Support

For new model requests or issues:
- Contact Magically support for model whitelisting
- Check test reports for API health status
- Review error messages for troubleshooting guidance
