# Convex Todo App Template

A complete React Native todo app with Convex backend and OAuth authentication.

## Features

- ✅ **Real-time sync** with Convex database
- ✅ **OAuth authentication** via Magically.life
- ✅ **Todo CRUD operations** (Create, Read, Update, Delete)
- ✅ **User isolation** - each user sees only their tasks
- ✅ **Branded login screen** with OAuth flow
- ✅ **TypeScript support** throughout

## Configuration

The app is pre-configured with:

- **Convex URL**: `https://calculating-wombat-46.convex.cloud`
- **OAuth Client ID**: `6hvWxKieiV0XuW-tgMvKVHJQpvqXTB7b`
- **Auth URLs**: 
  - Authorize: `https://magically.life/api/oauth/authorize`
  - Token: `https://magically.life/api/oauth/token`

## File Structure

```
├── App.tsx                          # Main app with ConvexProvider
├── convex/
│   ├── schema.ts                    # Database schema (users, tasks)
│   ├── users.ts                     # User management functions
│   ├── tasks.ts                     # Task CRUD operations
│   └── _generated/                  # Generated Convex types
├── libs/
│   └── convex.ts                    # Convex client setup
├── stores/
│   └── oauthStore.ts               # OAuth authentication store
├── screens/
│   ├── HomeScreen.tsx              # Todo app main screen
│   └── OAuthLoginScreen.tsx        # Branded login screen
└── navigation/
    └── index.tsx                   # Auth-aware navigation
```

## OAuth Flow

1. **User opens app** → sees login screen if not authenticated
2. **Clicks "Continue with Magically"** → opens browser
3. **Browser redirects** to `https://magically.life/api/oauth/authorize`
4. **User logs in** to Magically.life (if not already)
5. **Redirects back** to app with authorization code
6. **App exchanges code** for JWT token
7. **Sets Convex auth** and user is logged in

## Database Schema

### Users Table
```typescript
{
  _id: Id<"users">,
  name: string,
  email: string,
  authId: string,        // From OAuth token (user.id)
  _creationTime: number
}
```

### Tasks Table
```typescript
{
  _id: Id<"tasks">,
  title: string,
  description?: string,
  completed: boolean,
  userId: Id<"users">,
  priority: "low" | "medium" | "high",
  dueDate?: number,
  createdAt: number,
  updatedAt: number,
  _creationTime: number
}
```

## Testing

1. **Deploy to Expo Snack** or run locally
2. **Open app** → should show login screen
3. **Click "Continue with Magically"** → browser opens
4. **Complete OAuth flow** → redirects back to app
5. **Add/edit/delete tasks** → should sync in real-time
6. **Sign out and back in** → tasks should persist

## Dependencies

- `convex` - Real-time database and backend
- `expo-web-browser` - OAuth browser flow
- `expo-auth-session` - OAuth session handling
- `zustand` - State management
- `@react-navigation/native` - Navigation

## Notes

- **Expo Snack compatible** - minimal dependencies
- **Real Convex deployment** - not a mock
- **Working OAuth** - connects to actual Magically.life auth
- **TypeScript** - full type safety
- **Production ready** - error handling and loading states
