import { createClient } from '@supabase/supabase-js';
import { SUPABASE_CONFIG, PROJECT_ID } from '../constants';

// Create Supabase client with app isolation
export const supabase = createClient(
  SUPABASE_CONFIG.url,
  SUPABASE_CONFIG.anon<PERSON>ey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
    global: {
      headers: {
        'x-app-id': APP_ID,
      },
    },
  }
);

// Database Types
export interface Database {
  public: {
    Tables: {
      [`app_${PROJECT_ID}_users`]: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          provider: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          provider: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          provider?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}

// Helper function to get table name with app prefix
export const getTableName = (tableName: string) => `app_${APP_ID}_${tableName}`;

// Auth helper functions
export const getCurrentUser = () => {
  return supabase.auth.getUser();
};

export const signOut = () => {
  return supabase.auth.signOut();
};

// Profile management
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from(getTableName('users'))
    .select('*')
    .eq('id', userId)
    .single();
  
  if (error && error.code !== 'PGRST116') {
    throw error;
  }
  
  return data;
};

export const createUserProfile = async (profile: Database['public']['Tables'][`app_${APP_ID}_users`]['Insert']) => {
  const { data, error } = await supabase
    .from(getTableName('users'))
    .insert(profile)
    .select()
    .single();
  
  if (error) {
    throw error;
  }
  
  return data;
};

export const updateUserProfile = async (userId: string, updates: Database['public']['Tables'][`app_${APP_ID}_users`]['Update']) => {
  const { data, error } = await supabase
    .from(getTableName('users'))
    .update(updates)
    .eq('id', userId)
    .select()
    .single();
  
  if (error) {
    throw error;
  }
  
  return data;
};