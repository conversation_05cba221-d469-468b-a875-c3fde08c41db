# Wonka

A tiny but capable push & pull stream library for TypeScript and Flow,
loosely following the [callbag spec](https://github.com/callbag/callbag)

> **NOTE:** The currently released version v6 is only compatible now with TypeScript, Flow, and JavaScript.
> If you're looking for Reason/OCaml/esy/dune support, please check v5, and if you're looking for the legacy version
> of this library check v4.

<br>
<a href="https://npmjs.com/package/wonka">
  <img alt="NPM Version" src="https://img.shields.io/npm/v/wonka.svg" />
</a>
<a href="https://npmjs.com/package/wonka">
  <img alt="License" src="https://img.shields.io/npm/l/wonka.svg" />
</a>
<a href="https://coveralls.io/github/kitten/wonka?branch=master">
  <img src="https://coveralls.io/repos/github/kitten/wonka/badge.svg?branch=master" alt="Test Coverage" />
</a>
<a href="https://bundlephobia.com/result?p=wonka">
  <img alt="Minified gzip size" src="https://img.shields.io/bundlephobia/minzip/wonka.svg?label=gzip%20size" />
</a>
<br>

> “There’s no earthly way of knowing<br>
> Which direction we are going<br>
> There’s no knowing where we’re rowing<br>
> Or which way the river’s flowing” － **Willy Wonka**

<br>

![Wonka](/docs/wonka.jpg?raw=true)

Wonka is a lightweight iterable and observable library loosely based on
the [callbag spec](https://github.com/callbag/callbag). It exposes a set of helpers to create streams,
which are sources of multiple values, which allow you to create, transform
and consume event streams or iterable sets of data.

## [Documentation](https://wonka.kitten.sh/)

**See the documentation at [wonka.kitten.sh](https://wonka.kitten.sh)** for more information about using `wonka`!

- [Introduction](https://wonka.kitten.sh/)
- [**Getting started**](https://wonka.kitten.sh/getting-started)
- [Basics](https://wonka.kitten.sh/basics/)
- [API Reference](https://wonka.kitten.sh/api/)

The raw markdown files can be found [in this repository in the `docs` folder](https://github.com/kitten/wonka/tree/master/docs).
