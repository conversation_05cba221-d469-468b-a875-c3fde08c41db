{"name": "ts-object-utils", "version": "0.0.5", "description": "Best type safe way to check null or undefined in typescript", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "release": "npm run build && npm publish", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/naqvitalha/ts-object-utils.git"}, "author": "naqvitalha <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/naqvitalha/ts-object-utils/issues"}, "homepage": "https://github.com/naqvitalha/ts-object-utils#readme"}