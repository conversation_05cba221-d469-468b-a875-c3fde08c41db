import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';
import { colors, priorityColors } from '../constants';
import { useOAuthStore } from '../stores/oauthStore';

export default function HomeScreen() {
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [isAddingTask, setIsAddingTask] = useState(false);
  const { user, signOut } = useOAuthStore();

  // Get current user from Convex
  const currentUser = useQuery(api.users.getCurrentUser,
    user ? { authId: user.id } : "skip"
  );

  // Get tasks for current user
  const tasks = useQuery(api.tasks.getTasks,
    currentUser ? { userId: currentUser._id } : "skip"
  );

  // Mutations
  const createTask = useMutation(api.tasks.createTask);
  const toggleTask = useMutation(api.tasks.toggleTask);
  const deleteTask = useMutation(api.tasks.deleteTask);
  const getOrCreateUser = useMutation(api.users.getOrCreateUser);

  // Initialize user in Convex if needed
  React.useEffect(() => {
    if (user && !currentUser) {
      getOrCreateUser({
        authId: user.id,
        name: user.name || 'User',
        email: user.email,
      });
    }
  }, [user, currentUser]);

  const handleAddTask = async () => {
    if (!newTaskTitle.trim() || !currentUser) return;

    try {
      await createTask({
        title: newTaskTitle.trim(),
        userId: currentUser._id,
        priority: 'medium',
      });
      setNewTaskTitle('');
      setIsAddingTask(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to create task');
    }
  };

  const handleToggleTask = async (taskId: string) => {
    try {
      await toggleTask({ id: taskId });
    } catch (error) {
      Alert.alert('Error', 'Failed to update task');
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    try {
      await deleteTask({ id: taskId });
    } catch (error) {
      Alert.alert('Error', 'Failed to delete task');
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      Alert.alert('Error', 'Failed to sign out');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>My Tasks</Text>
          <Text style={styles.subtitle}>Welcome back, {user?.name || 'User'}!</Text>
        </View>
        <TouchableOpacity onPress={handleSignOut} style={styles.signOutButton}>
          <Feather name="log-out" size={20} color={colors.text.secondary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Add Task Section */}
        <View style={styles.addTaskSection}>
          {isAddingTask ? (
            <View style={styles.addTaskForm}>
              <TextInput
                style={styles.taskInput}
                placeholder="What needs to be done?"
                value={newTaskTitle}
                onChangeText={setNewTaskTitle}
                autoFocus
                onSubmitEditing={handleAddTask}
              />
              <View style={styles.addTaskButtons}>
                <TouchableOpacity onPress={handleAddTask} style={styles.saveButton}>
                  <Feather name="check" size={16} color="white" />
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setIsAddingTask(false);
                    setNewTaskTitle('');
                  }}
                  style={styles.cancelButton}
                >
                  <Feather name="x" size={16} color={colors.text.secondary} />
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <TouchableOpacity
              onPress={() => setIsAddingTask(true)}
              style={styles.addTaskButton}
            >
              <Feather name="plus" size={20} color={colors.primary} />
              <Text style={styles.addTaskText}>Add new task</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Tasks List */}
        <View style={styles.tasksSection}>
          {tasks?.length === 0 ? (
            <View style={styles.emptyState}>
              <Feather name="check-circle" size={48} color={colors.text.secondary} />
              <Text style={styles.emptyStateText}>No tasks yet</Text>
              <Text style={styles.emptyStateSubtext}>Add your first task to get started!</Text>
            </View>
          ) : (
            tasks?.map((task) => (
              <View key={task._id} style={styles.taskItem}>
                <TouchableOpacity
                  onPress={() => handleToggleTask(task._id)}
                  style={styles.taskCheckbox}
                >
                  <Feather
                    name={task.completed ? "check-circle" : "circle"}
                    size={20}
                    color={task.completed ? colors.success : colors.text.secondary}
                  />
                </TouchableOpacity>
                <View style={styles.taskContent}>
                  <Text style={[
                    styles.taskTitle,
                    task.completed && styles.taskTitleCompleted
                  ]}>
                    {task.title}
                  </Text>
                  <View style={[
                    styles.priorityBadge,
                    { backgroundColor: priorityColors[task.priority].background }
                  ]}>
                    <Text style={[
                      styles.priorityText,
                      { color: priorityColors[task.priority].text }
                    ]}>
                      {task.priority.toUpperCase()}
                    </Text>
                  </View>
                </View>
                <TouchableOpacity
                  onPress={() => handleDeleteTask(task._id)}
                  style={styles.deleteButton}
                >
                  <Feather name="trash-2" size={16} color={colors.error} />
                </TouchableOpacity>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  subtitle: {
    fontSize: 14,
    color: colors.text.secondary,
    marginTop: 2,
  },
  signOutButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  addTaskSection: {
    padding: 20,
  },
  addTaskButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.surface,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.primary,
    borderStyle: 'dashed',
  },
  addTaskText: {
    marginLeft: 12,
    fontSize: 16,
    color: colors.primary,
    fontWeight: '500',
  },
  addTaskForm: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  taskInput: {
    flex: 1,
    padding: 16,
    backgroundColor: colors.surface,
    borderRadius: 12,
    fontSize: 16,
    color: colors.text.primary,
  },
  addTaskButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  saveButton: {
    backgroundColor: colors.success,
    padding: 12,
    borderRadius: 8,
  },
  cancelButton: {
    backgroundColor: colors.surface,
    padding: 12,
    borderRadius: 8,
  },
  tasksSection: {
    paddingHorizontal: 20,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.text.secondary,
    marginTop: 4,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.surface,
    borderRadius: 12,
    marginBottom: 8,
  },
  taskCheckbox: {
    marginRight: 12,
  },
  taskContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  taskTitle: {
    flex: 1,
    fontSize: 16,
    color: colors.text.primary,
    marginRight: 8,
  },
  taskTitleCompleted: {
    textDecorationLine: 'line-through',
    color: colors.text.secondary,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  deleteButton: {
    padding: 8,
  },
});
