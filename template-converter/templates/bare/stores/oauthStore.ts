import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as WebBrowser from 'expo-web-browser';
import * as AuthSession from 'expo-auth-session';
import { setAuthToken, clearAuth } from '../libs/convex';
import {PROJECT_ID} from "../constants";

export interface User {
  id: string;
  email: string;
  name?: string;
}

export interface OAuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

export interface OAuthActions {
  signIn: () => Promise<void>;
  signOut: () => Promise<void>;
  setError: (error: string | null) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  setUser: (user: User | null) => void;
}

export type OAuthStore = OAuthState & OAuthActions;

// OAuth configuration - TODO: Replace with actual project values
const OAUTH_CONFIG = {
  clientId: '6hvWxKieiV0XuW-tgMvKVHJQpvqXTB7b', // Will be replaced with actual client ID
  projectId: PROJECT_ID, // Will be replaced with actual project ID
  authUrl: 'http://localhost:3000/api/oauth/authorize',
  callbackUrl: 'http://localhost:3000/projects/PROJECT_ID/callback/magically',
};

export const useOAuthStore = create<OAuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isLoading: false,
      isAuthenticated: false,
      error: null,

      // OAuth sign in - opens browser to your platform
      signIn: async () => {
        set({ isLoading: true, error: null });

        try {
          const redirectUri = OAUTH_CONFIG.callbackUrl.replace('PROJECT_ID', OAUTH_CONFIG.projectId);

          console.log('OAuth redirect URI:', redirectUri);

          // Set up message listener BEFORE opening browser
          const messagePromise = new Promise((resolve, reject) => {
            const handleMessage = (event) => {
              console.log('Received message:', event.data);

              if (event.data?.type === 'magically-auth-callback') {
                console.log('OAuth callback message received');
                window.removeEventListener('message', handleMessage);
                resolve(event.data);
              }
            };

            window.addEventListener('message', handleMessage);

            // Timeout after 5 minutes
            setTimeout(() => {
              console.log('OAuth message timeout');
              window.removeEventListener('message', handleMessage);
              reject(new Error('Authentication timeout - no response received'));
            }, 5 * 60 * 1000);
          });

          const authUrl = `${OAUTH_CONFIG.authUrl}?client_id=${OAUTH_CONFIG.clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=read write`;

          console.log('Opening OAuth browser...');

          // Open browser (don't await - we'll wait for postMessage instead)
          WebBrowser.openAuthSessionAsync(authUrl, redirectUri);

          // Wait for postMessage from callback handler
          console.log('Waiting for OAuth callback message...');
          const tokenData = await messagePromise;

          console.log('Processing OAuth tokens...');

          // Process the received tokens
          if (tokenData.accessToken) {
            // Store token
            await AsyncStorage.setItem('auth_token', tokenData.accessToken);

            // Set auth token for Convex (new API - pass async function)
            setAuthToken(async () => {
              return tokenData.accessToken;
            });

            // Store user data
            const userData = tokenData.user;
            await AsyncStorage.setItem('user_data', JSON.stringify(userData));

            // Update store
            set({
              user: userData,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });

            console.log('OAuth sign in successful');
          } else {
            throw new Error('No access token received from callback');
          }

        } catch (error) {
          console.error('OAuth sign in failed:', error);
          set({
            error: error instanceof Error ? error.message : 'Sign in failed',
            isLoading: false
          });
          throw error;
        }
      },

      signOut: async () => {
        set({ isLoading: true });

        try {
          await AsyncStorage.removeItem('auth_token');
          await AsyncStorage.removeItem('user_data');

          // Clear Convex auth (new API - pass null function)
          setAuthToken(async () => {
            return null;
          });

          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Sign out failed',
            isLoading: false
          });
          throw error;
        }
      },

      // State management actions
      setError: (error: string | null) => set({ error }),
      clearError: () => set({ error: null }),
      setLoading: (loading: boolean) => set({ isLoading: loading }),
      setUser: (user: User | null) => set({ 
        user, 
        isAuthenticated: !!user 
      }),
    }),
    {
      name: 'oauth-storage',
      storage: {
        getItem: (name) => AsyncStorage.getItem(name),
        setItem: (name, value) => AsyncStorage.setItem(name, value),
        removeItem: (name) => AsyncStorage.removeItem(name),
      },
      // Only persist user data, not loading states
      partialize: (state) => ({ 
        user: state.user,
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
);

// Note: Token exchange is now handled by the server-side callback handler
// The app receives tokens via postMessage from the callback page

// Initialize auth on app start
export const initializeAuth = async () => {
  try {
    const token = await AsyncStorage.getItem('auth_token');
    const userData = await AsyncStorage.getItem('user_data');

    if (token && userData) {
      // Set auth token for Convex (new API - pass async function)
      setAuthToken(async () => {
        return token;
      });
      const user = JSON.parse(userData);
      useOAuthStore.getState().setUser(user);
    }
  } catch (error) {
    console.error('Failed to initialize auth:', error);
  }
};
