{"name": "checkpoint-pro", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:web": "expo export --platform web", "deploy:testflight": "npx testflight --non-interactive"}, "dependencies": {"@expo/metro-runtime": "~4.0.1", "@react-navigation/native-stack": "*", "@react-native-async-storage/async-storage": "*", "expo-av": "*", "expo-linear-gradient": "*", "lucide-react-native": "*", "@expo/vector-icons": "*", "@react-navigation/native": "*", "expo": "~53.0.9", "expo-font": "~13.3.1", "expo-linking": "*", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "*", "expo-system-ui": "~5.0.7", "expo-web-browser": "*", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-reanimated": "*", "react-native-safe-area-context": "*", "react-native-screens": "*", "react-native-web": "~0.20.0", "@react-navigation/bottom-tabs": "*", "expo-clipboard": "*", "react-native-gesture-handler": "*", "react-native-svg": "15.11.2", "sonner-native": "*", "expo-camera": "*", "@shopify/flash-list": "*", "react-hook-form": "*", "expo-auth-session": "*", "expo-crypto": "*", "expo-haptics": "*", "@react-navigation/drawer": "*", "@react-native-community/slider": "*", "expo-constants": "*", "expo-image-picker": "*", "@react-native-community/datetimepicker": "*", "expo-sensors": "*", "@react-native-community/netinfo": "*", "expo-audio": "*", "expo-blur": "*", "expo-file-system": "*", "expo-image-manipulator": "*", "expo-location": "*", "zustand": "*", "date-fns": "*", "react-native-webview": "*", "convex": "^1.5.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "typescript": "^5.3.3"}, "private": true}