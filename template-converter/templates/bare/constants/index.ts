// Project Configuration
export const PROJECT_ID: string = `9e3817f0-5287-4db0-9f53-e0bec7dea1b5`;
export const ENVIRONMENT: string = '%%ENVIRONMENT%%';

// Storage Keys (using project ID for uniqueness)
export const STORAGE_KEYS = {
    AUTH_STORE: `checkpoint_pro_${PROJECT_ID}_auth`,
    AUDIT_STORE: `checkpoint_pro_${PROJECT_ID}_audits`,
    CLIENT_STORE: `checkpoint_pro_${PROJECT_ID}_clients`,
    SETTINGS_STORE: `checkpoint_pro_${PROJECT_ID}_settings`,
};

// App Configuration
export const APP_CONFIG = {
    name: 'CheckPoint Pro',
    version: '1.0.0',
    description: 'Professional auditing solution with voice-to-text and compliance scoring',
};

// Supabase Configuration
export const SUPABASE_CONFIG = {
    url: 'https://ucxsqgbwnnnbfozyqwwj.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVjeHNxZ2J3bm5uYmZvenlxd3dqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0ODAxODYsImV4cCI6MjA2NTA1NjE4Nn0.3xmdrTKXKOFJgjAwnoYFswGTFbuOIv6T8JrG2A0eRaU',
};

// Platform Detection
export const getPlatform = () => {
    if (typeof window !== 'undefined') {
        return 'web';
    }
    return 'native';
};

// Redirect URLs for OAuth
export const getRedirectUrl = () => {
    const platform = getPlatform();
    if (platform === 'web') {
        let origin = `http://locahost:3000`;
        if (ENVIRONMENT === "local") {
            origin = "http://locahost:3000"
        }
        return `${origin}/projects/${PROJECT_ID}/callback/google`;
    }
    return `exp://u.expo.dev/${PROJECT_ID}/callback/google`;
};

// Allowed Origins for Security
export const ALLOWED_ORIGINS = [
    'http://localhost:3000',
    'https://magically.life',
    'https://trymagically.com'
];

// Export colors
export { colors, darkColors, getColors, colorUtils, priorityColors, statusColors } from './colors';