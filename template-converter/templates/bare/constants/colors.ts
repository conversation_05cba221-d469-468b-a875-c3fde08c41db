/**
 * Color system for the Todo App
 * Compatible with both light and dark themes
 */

export const colors = {
  // Primary brand colors
  primary: '#6366f1',        // Indigo-500
  primaryLight: '#818cf8',   // Indigo-400
  primaryDark: '#4f46e5',    // Indigo-600

  // Background colors
  background: '#ffffff',     // White
  surface: '#f8fafc',        // Slate-50
  surfaceElevated: '#f1f5f9', // Slate-100

  // Text colors
  text: {
    primary: '#0f172a',      // Slate-900
    secondary: '#64748b',    // Slate-500
    tertiary: '#94a3b8',     // Slate-400
    inverse: '#ffffff',      // White
  },

  // Semantic colors
  success: '#10b981',        // Emerald-500
  successLight: '#34d399',   // Emerald-400
  successDark: '#059669',    // Emerald-600

  warning: '#f59e0b',        // Amber-500
  warningLight: '#fbbf24',   // Amber-400
  warningDark: '#d97706',    // Amber-600

  error: '#ef4444',          // Red-500
  errorLight: '#f87171',     // Red-400
  errorDark: '#dc2626',      // Red-600

  info: '#3b82f6',           // Blue-500
  infoLight: '#60a5fa',      // Blue-400
  infoDark: '#2563eb',       // Blue-600

  // Border and separator colors
  border: '#e2e8f0',         // Slate-200
  separator: '#e2e8f0',      // Slate-200
  divider: '#cbd5e1',        // Slate-300

  // Interactive states
  hover: '#f1f5f9',          // Slate-100
  pressed: '#e2e8f0',        // Slate-200
  disabled: '#f8fafc',       // Slate-50
  disabledText: '#cbd5e1',   // Slate-300

  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.25)',
  overlayDark: 'rgba(0, 0, 0, 0.75)',

  // Transparent colors
  transparent: 'transparent',
};

/**
 * Dark theme colors (for future use)
 */
export const darkColors = {
  // Primary brand colors (same as light)
  primary: '#6366f1',
  primaryLight: '#818cf8',
  primaryDark: '#4f46e5',

  // Background colors
  background: '#0f172a',     // Slate-900
  surface: '#1e293b',        // Slate-800
  surfaceElevated: '#334155', // Slate-700

  // Text colors
  text: {
    primary: '#f8fafc',      // Slate-50
    secondary: '#cbd5e1',    // Slate-300
    tertiary: '#94a3b8',     // Slate-400
    inverse: '#0f172a',      // Slate-900
  },

  // Semantic colors (same as light)
  success: '#10b981',
  successLight: '#34d399',
  successDark: '#059669',

  warning: '#f59e0b',
  warningLight: '#fbbf24',
  warningDark: '#d97706',

  error: '#ef4444',
  errorLight: '#f87171',
  errorDark: '#dc2626',

  info: '#3b82f6',
  infoLight: '#60a5fa',
  infoDark: '#2563eb',

  // Border and separator colors
  border: '#334155',         // Slate-700
  separator: '#334155',      // Slate-700
  divider: '#475569',        // Slate-600

  // Interactive states
  hover: '#334155',          // Slate-700
  pressed: '#475569',        // Slate-600
  disabled: '#1e293b',       // Slate-800
  disabledText: '#64748b',   // Slate-500

  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.75)',
  overlayLight: 'rgba(0, 0, 0, 0.5)',
  overlayDark: 'rgba(0, 0, 0, 0.9)',

  // Transparent colors
  transparent: 'transparent',
};

/**
 * Get colors based on theme
 */
export const getColors = (isDark: boolean = false) => {
  return isDark ? darkColors : colors;
};

/**
 * Color utilities
 */
export const colorUtils = {
  /**
   * Add opacity to a hex color
   */
  withOpacity: (color: string, opacity: number): string => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  },

  /**
   * Get contrast color (black or white) for a given background
   */
  getContrastColor: (backgroundColor: string): string => {
    // Simple contrast calculation
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  },
};

/**
 * Priority colors for tasks
 */
export const priorityColors = {
  low: {
    background: colorUtils.withOpacity(colors.success, 0.1),
    border: colors.success,
    text: colors.success,
  },
  medium: {
    background: colorUtils.withOpacity(colors.warning, 0.1),
    border: colors.warning,
    text: colors.warning,
  },
  high: {
    background: colorUtils.withOpacity(colors.error, 0.1),
    border: colors.error,
    text: colors.error,
  },
};

/**
 * Status colors for tasks
 */
export const statusColors = {
  pending: {
    background: colorUtils.withOpacity(colors.info, 0.1),
    border: colors.info,
    text: colors.info,
  },
  completed: {
    background: colorUtils.withOpacity(colors.success, 0.1),
    border: colors.success,
    text: colors.success,
  },
  overdue: {
    background: colorUtils.withOpacity(colors.error, 0.1),
    border: colors.error,
    text: colors.error,
  },
};

// Export default colors for backward compatibility
export default colors;
