import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined' | 'gradient';
  padding?: 'none' | 'small' | 'medium' | 'large';
  disabled?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  style,
  onPress,
  variant = 'default',
  padding = 'medium',
  disabled = false,
}) => {
  const getCardStyle = (): ViewStyle => {
    let cardStyle = { ...styles.base, ...styles[padding] };
    
    switch (variant) {
      case 'elevated':
        cardStyle = { ...cardStyle, ...styles.elevated };
        break;
      case 'outlined':
        cardStyle = { ...cardStyle, ...styles.outlined };
        break;
      case 'gradient':
        cardStyle = { ...cardStyle, ...styles.gradient };
        break;
      default:
        cardStyle = { ...cardStyle, ...styles.default };
    }
    
    return cardStyle;
  };

  const CardContent = () => (
    <View style={[getCardStyle(), style, disabled && styles.disabled]}>
      {children}
    </View>
  );

  if (variant === 'gradient') {
    const Wrapper = onPress ? TouchableOpacity : View;
    return (
      <Wrapper
        onPress={onPress}
        disabled={disabled}
        activeOpacity={onPress ? 0.8 : 1}
        style={[styles.base, styles[padding], style, disabled && styles.disabled]}
      >
        <LinearGradient
          colors={['#F8FAFC', '#E2E8F0']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradientBackground}
        >
          {children}
        </LinearGradient>
      </Wrapper>
    );
  }

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.8}
        style={[getCardStyle(), style, disabled && styles.disabled]}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return <CardContent />;
};

const styles = StyleSheet.create({
  base: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  default: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#F1F5F9',
  },
  elevated: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  outlined: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1.5,
    borderColor: '#E2E8F0',
  },
  gradient: {
    backgroundColor: 'transparent',
  },
  gradientBackground: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  none: {
    padding: 0,
  },
  small: {
    padding: 12,
  },
  medium: {
    padding: 16,
  },
  large: {
    padding: 24,
  },
  disabled: {
    opacity: 0.6,
  },
});